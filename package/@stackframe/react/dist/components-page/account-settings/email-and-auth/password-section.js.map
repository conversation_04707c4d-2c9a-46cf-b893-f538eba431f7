{"version": 3, "sources": ["../../../../src/components-page/account-settings/email-and-auth/password-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { getPasswordError } from '@stackframe/stack-shared/dist/helpers/password';\nimport { passwordSchema as schemaFieldsPasswordSchema, yupObject, yupString } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronously, runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Label, PasswordInput, Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { useStackApp } from '../../..';\nimport { FormWarningText } from \"../../../components/elements/form-warning\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\n\nexport function PasswordSection() {\n  const { t } = useTranslation();\n  const user = useUser({ or: \"throw\" });\n  const contactChannels = user.useContactChannels();\n  const [changingPassword, setChangingPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const project = useStackApp().useProject();\n\n  const passwordSchema = yupObject({\n    oldPassword: user.hasPassword ? schemaFieldsPasswordSchema.defined().nonEmpty(t('Please enter your old password')) : yupString(),\n    newPassword: schemaFieldsPasswordSchema.defined().nonEmpty(t('Please enter your password')).test({\n      name: 'is-valid-password',\n      test: (value, ctx) => {\n        const error = getPasswordError(value);\n        if (error) {\n          return ctx.createError({ message: error.message });\n        } else {\n          return true;\n        }\n      }\n    }),\n    newPasswordRepeat: yupString().nullable().oneOf([yup.ref('newPassword'), \"\", null], t('Passwords do not match')).defined().nonEmpty(t('Please repeat your password'))\n  });\n\n  const { register, handleSubmit, setError, formState: { errors }, clearErrors, reset } = useForm({\n    resolver: yupResolver(passwordSchema)\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const hasValidEmail = contactChannels.filter(x => x.type === 'email' && x.usedForAuth).length > 0;\n\n  const onSubmit = async (data: yup.InferType<typeof passwordSchema>) => {\n    setLoading(true);\n    try {\n      const { oldPassword, newPassword } = data;\n      const error = user.hasPassword\n        ? await user.updatePassword({ oldPassword: oldPassword!, newPassword })\n        : await user.setPassword({ password: newPassword! });\n      if (error) {\n        setError('oldPassword', { type: 'manual', message: t('Incorrect password') });\n      } else {\n        reset();\n        setChangingPassword(false);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const registerPassword = register('newPassword');\n  const registerPasswordRepeat = register('newPasswordRepeat');\n\n  if (!project.config.credentialEnabled) {\n    return null;\n  }\n\n  return (\n    <Section\n      title={t(\"Password\")}\n      description={user.hasPassword ? t(\"Update your password\") : t(\"Set a password for your account\")}\n    >\n      <div className='flex flex-col gap-4'>\n        {!changingPassword ? (\n          hasValidEmail ? (\n            <Button\n              variant='secondary'\n              onClick={() => setChangingPassword(true)}\n            >\n              {user.hasPassword ? t(\"Update password\") : t(\"Set password\")}\n            </Button>\n          ) : (\n            <Typography variant='secondary' type='label'>{t(\"To set a password, please add a sign-in email.\")}</Typography>\n          )\n        ) : (\n          <form\n            onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n            noValidate\n          >\n            {user.hasPassword && (\n              <>\n                <Label htmlFor=\"old-password\" className=\"mb-1\">{t(\"Old password\")}</Label>\n                <Input\n                  id=\"old-password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  {...register(\"oldPassword\")}\n                />\n                <FormWarningText text={errors.oldPassword?.message?.toString()} />\n              </>\n            )}\n\n            <Label htmlFor=\"new-password\" className=\"mt-4 mb-1\">{t(\"New password\")}</Label>\n            <PasswordInput\n              id=\"new-password\"\n              autoComplete=\"new-password\"\n              {...registerPassword}\n              onChange={(e) => {\n                clearErrors('newPassword');\n                clearErrors('newPasswordRepeat');\n                runAsynchronously(registerPassword.onChange(e));\n              }}\n            />\n            <FormWarningText text={errors.newPassword?.message?.toString()} />\n\n            <Label htmlFor=\"repeat-password\" className=\"mt-4 mb-1\">{t(\"Repeat new password\")}</Label>\n            <PasswordInput\n              id=\"repeat-password\"\n              autoComplete=\"new-password\"\n              {...registerPasswordRepeat}\n              onChange={(e) => {\n                clearErrors('newPassword');\n                clearErrors('newPasswordRepeat');\n                runAsynchronously(registerPasswordRepeat.onChange(e));\n              }}\n            />\n            <FormWarningText text={errors.newPasswordRepeat?.message?.toString()} />\n\n            <div className=\"mt-6 flex gap-4\">\n              <Button type=\"submit\" loading={loading}>\n                {user.hasPassword ? t(\"Update Password\") : t(\"Set Password\")}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                onClick={() => {\n                  setChangingPassword(false);\n                  reset();\n                }}\n              >\n                {t(\"Cancel\")}\n              </Button>\n            </div>\n          </form>\n        )}\n      </div>\n    </Section>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,iBAA4B;AAC5B,sBAAiC;AACjC,2BAAmF;AACnF,sBAA8D;AAC9D,sBAAgE;AAChE,mBAAyB;AACzB,6BAAwB;AACxB,UAAqB;AACrB,eAA4B;AAC5B,0BAAgC;AAChC,mBAAwB;AACxB,0BAA+B;AAC/B,qBAAwB;AAmEZ;AAhEL,SAAS,kBAAkB;AAChC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,QAAQ,CAAC;AACpC,QAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,uBAAS,KAAK;AAC9D,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAC5C,QAAM,cAAU,sBAAY,EAAE,WAAW;AAEzC,QAAM,qBAAiB,gCAAU;AAAA,IAC/B,aAAa,KAAK,cAAc,qBAAAA,eAA2B,QAAQ,EAAE,SAAS,EAAE,gCAAgC,CAAC,QAAI,gCAAU;AAAA,IAC/H,aAAa,qBAAAA,eAA2B,QAAQ,EAAE,SAAS,EAAE,4BAA4B,CAAC,EAAE,KAAK;AAAA,MAC/F,MAAM;AAAA,MACN,MAAM,CAAC,OAAO,QAAQ;AACpB,cAAM,YAAQ,kCAAiB,KAAK;AACpC,YAAI,OAAO;AACT,iBAAO,IAAI,YAAY,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,QACnD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,uBAAmB,gCAAU,EAAE,SAAS,EAAE,MAAM,CAAK,QAAI,aAAa,GAAG,IAAI,IAAI,GAAG,EAAE,wBAAwB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,6BAA6B,CAAC;AAAA,EACtK,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,UAAU,WAAW,EAAE,OAAO,GAAG,aAAa,MAAM,QAAI,gCAAQ;AAAA,IAC9F,cAAU,wBAAY,cAAc;AAAA,EACtC,CAAC;AAGD,QAAM,gBAAgB,gBAAgB,OAAO,OAAK,EAAE,SAAS,WAAW,EAAE,WAAW,EAAE,SAAS;AAEhG,QAAM,WAAW,OAAO,SAA+C;AACrE,eAAW,IAAI;AACf,QAAI;AACF,YAAM,EAAE,aAAa,YAAY,IAAI;AACrC,YAAM,QAAQ,KAAK,cACf,MAAM,KAAK,eAAe,EAAE,aAA2B,YAAY,CAAC,IACpE,MAAM,KAAK,YAAY,EAAE,UAAU,YAAa,CAAC;AACrD,UAAI,OAAO;AACT,iBAAS,eAAe,EAAE,MAAM,UAAU,SAAS,EAAE,oBAAoB,EAAE,CAAC;AAAA,MAC9E,OAAO;AACL,cAAM;AACN,4BAAoB,KAAK;AAAA,MAC3B;AAAA,IACF,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,mBAAmB,SAAS,aAAa;AAC/C,QAAM,yBAAyB,SAAS,mBAAmB;AAE3D,MAAI,CAAC,QAAQ,OAAO,mBAAmB;AACrC,WAAO;AAAA,EACT;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,UAAU;AAAA,MACnB,aAAa,KAAK,cAAc,EAAE,sBAAsB,IAAI,EAAE,iCAAiC;AAAA,MAE/F,sDAAC,SAAI,WAAU,uBACZ,WAAC,mBACA,gBACE;AAAA,QAAC;AAAA;AAAA,UACC,SAAQ;AAAA,UACR,SAAS,MAAM,oBAAoB,IAAI;AAAA,UAEtC,eAAK,cAAc,EAAE,iBAAiB,IAAI,EAAE,cAAc;AAAA;AAAA,MAC7D,IAEA,4CAAC,8BAAW,SAAQ,aAAY,MAAK,SAAS,YAAE,gDAAgD,GAAE,IAGpG;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,WAAK,4CAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,UACnE,YAAU;AAAA,UAET;AAAA,iBAAK,eACJ,4EACE;AAAA,0DAAC,yBAAM,SAAQ,gBAAe,WAAU,QAAQ,YAAE,cAAc,GAAE;AAAA,cAClE;AAAA,gBAAC;AAAA;AAAA,kBACC,IAAG;AAAA,kBACH,MAAK;AAAA,kBACL,cAAa;AAAA,kBACZ,GAAG,SAAS,aAAa;AAAA;AAAA,cAC5B;AAAA,cACA,4CAAC,uCAAgB,MAAM,OAAO,aAAa,SAAS,SAAS,GAAG;AAAA,eAClE;AAAA,YAGF,4CAAC,yBAAM,SAAQ,gBAAe,WAAU,aAAa,YAAE,cAAc,GAAE;AAAA,YACvE;AAAA,cAAC;AAAA;AAAA,gBACC,IAAG;AAAA,gBACH,cAAa;AAAA,gBACZ,GAAG;AAAA,gBACJ,UAAU,CAAC,MAAM;AACf,8BAAY,aAAa;AACzB,8BAAY,mBAAmB;AAC/B,yDAAkB,iBAAiB,SAAS,CAAC,CAAC;AAAA,gBAChD;AAAA;AAAA,YACF;AAAA,YACA,4CAAC,uCAAgB,MAAM,OAAO,aAAa,SAAS,SAAS,GAAG;AAAA,YAEhE,4CAAC,yBAAM,SAAQ,mBAAkB,WAAU,aAAa,YAAE,qBAAqB,GAAE;AAAA,YACjF;AAAA,cAAC;AAAA;AAAA,gBACC,IAAG;AAAA,gBACH,cAAa;AAAA,gBACZ,GAAG;AAAA,gBACJ,UAAU,CAAC,MAAM;AACf,8BAAY,aAAa;AACzB,8BAAY,mBAAmB;AAC/B,yDAAkB,uBAAuB,SAAS,CAAC,CAAC;AAAA,gBACtD;AAAA;AAAA,YACF;AAAA,YACA,4CAAC,uCAAgB,MAAM,OAAO,mBAAmB,SAAS,SAAS,GAAG;AAAA,YAEtE,6CAAC,SAAI,WAAU,mBACb;AAAA,0DAAC,0BAAO,MAAK,UAAS,SACnB,eAAK,cAAc,EAAE,iBAAiB,IAAI,EAAE,cAAc,GAC7D;AAAA,cACA;AAAA,gBAAC;AAAA;AAAA,kBACC,SAAQ;AAAA,kBACR,SAAS,MAAM;AACb,wCAAoB,KAAK;AACzB,0BAAM;AAAA,kBACR;AAAA,kBAEC,YAAE,QAAQ;AAAA;AAAA,cACb;AAAA,eACF;AAAA;AAAA;AAAA,MACF,GAEJ;AAAA;AAAA,EACF;AAEJ;", "names": ["schemaFieldsPasswordSchema"]}