{"version": 3, "sources": ["../../../../src/components-page/account-settings/active-sessions/active-sessions-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { fromNow } from \"@stackframe/stack-shared/dist/utils/dates\";\nimport { captureError } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { runAsynchronously } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { ActionCell, Badge, Button, Skeleton, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Typography } from \"@stackframe/stack-ui\";\nimport { useEffect, useState } from \"react\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { ActiveSession } from \"../../../lib/stack-app/users\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { PageLayout } from \"../page-layout\";\n\nexport function ActiveSessionsPage() {\n  const { t } = useTranslation();\n  const user = useUser({ or: \"throw\" });\n  const [isLoading, setIsLoading] = useState(true);\n  const [isRevokingAll, setIsRevokingAll] = useState(false);\n  const [sessions, setSessions] = useState<ActiveSession[]>([]);\n  const [showConfirmRevokeAll, setShowConfirmRevokeAll] = useState(false);\n\n  // Fetch sessions when component mounts\n  useEffect(() => {\n    runAsynchronously(async () => {\n      setIsLoading(true);\n      const sessionsData = await user.getActiveSessions();\n      const enhancedSessions = sessionsData;\n      setSessions(enhancedSessions);\n      setIsLoading(false);\n    });\n  }, [user]);\n\n  const handleRevokeSession = async (sessionId: string) => {\n    try {\n      await user.revokeSession(sessionId);\n\n      // Remove the session from the list\n      setSessions(prev => prev.filter(session => session.id !== sessionId));\n    } catch (error) {\n      captureError(\"Failed to revoke session\", { sessionId ,error });\n      throw error;\n    }\n  };\n\n  const handleRevokeAllSessions = async () => {\n    setIsRevokingAll(true);\n    try {\n      const deletionPromises = sessions\n        .filter(session => !session.isCurrentSession)\n        .map(session => user.revokeSession(session.id));\n      await Promise.all(deletionPromises);\n      setSessions(prevSessions => prevSessions.filter(session => session.isCurrentSession));\n    } catch (error) {\n      captureError(\"Failed to revoke all sessions\", { error, sessionIds: sessions.map(session => session.id) });\n      throw error;\n    } finally {\n      setIsRevokingAll(false);\n      setShowConfirmRevokeAll(false);\n    }\n  };\n\n  return (\n    <PageLayout>\n      <div>\n        <div className=\"flex justify-between items-center mb-2\">\n          <Typography className='font-medium'>{t(\"Active Sessions\")}</Typography>\n          {sessions.filter(s => !s.isCurrentSession).length > 0 && !isLoading && (\n            showConfirmRevokeAll ? (\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"destructive\"\n                  size=\"sm\"\n                  loading={isRevokingAll}\n                  onClick={handleRevokeAllSessions}\n                >\n                  {t(\"Confirm\")}\n                </Button>\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  disabled={isRevokingAll}\n                  onClick={() => setShowConfirmRevokeAll(false)}\n                >\n                  {t(\"Cancel\")}\n                </Button>\n              </div>\n            ) : (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowConfirmRevokeAll(true)}\n              >\n                {t(\"Revoke All Other Sessions\")}\n              </Button>\n            )\n          )}\n        </div>\n        <Typography variant='secondary' type='footnote' className=\"mb-4\">\n          {t(\"These are devices where you're currently logged in. You can revoke access to end a session.\")}\n        </Typography>\n\n        {isLoading ? (\n          <Skeleton className=\"h-[300px] w-full rounded-md\" />\n        ) : (\n          <div className='border rounded-md'>\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead className=\"w-[200px]\">{t(\"Session\")}</TableHead>\n                  <TableHead className=\"w-[150px]\">{t(\"IP Address\")}</TableHead>\n                  <TableHead className=\"w-[150px]\">{t(\"Location\")}</TableHead>\n                  <TableHead className=\"w-[150px]\">{t(\"Last used\")}</TableHead>\n                  <TableHead className=\"w-[80px]\"></TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {sessions.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={5} className=\"text-center py-6\">\n                      <Typography variant=\"secondary\">{t(\"No active sessions found\")}</Typography>\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  sessions.map((session) => (\n                    <TableRow key={session.id}>\n                      <TableCell>\n                        <div className=\"flex flex-col\">\n                          {/* We currently do not save any usefull information about the user, in the future, the name should probably say what kind of session it is (e.g. cli, browser, maybe what auth method was used) */}\n                          <Typography>{session.isCurrentSession ? t(\"Current Session\") : t(\"Other Session\")}</Typography>\n                          {session.isImpersonation && <Badge variant=\"secondary\" className=\"w-fit mt-1\">{t(\"Impersonation\")}</Badge>}\n                          <Typography variant='secondary' type='footnote'>\n                            {t(\"Signed in {time}\", { time: new Date(session.createdAt).toLocaleDateString() })}\n                          </Typography>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Typography>{session.geoInfo?.ip || t('-')}</Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography>{session.geoInfo?.cityName || t('Unknown')}</Typography>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex flex-col\">\n                          <Typography>{session.lastUsedAt ? fromNow(new Date(session.lastUsedAt)) : t(\"Never\")}</Typography>\n                          <Typography variant='secondary' type='footnote' title={session.lastUsedAt ? new Date(session.lastUsedAt).toLocaleString() : \"\"}>\n                            {session.lastUsedAt ? new Date(session.lastUsedAt).toLocaleDateString() : \"\"}\n                          </Typography>\n                        </div>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        <ActionCell\n                          items={[\n                            {\n                              item: t(\"Revoke\"),\n                              onClick: () => handleRevokeSession(session.id),\n                              danger: true,\n                              disabled: session.isCurrentSession,\n                              disabledTooltip: session.isCurrentSession ? t(\"You cannot revoke your current session\") : undefined,\n                            },\n                          ]}\n                        />\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </div>\n        )}\n      </div>\n    </PageLayout>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,mBAAwB;AACxB,oBAA6B;AAC7B,sBAAkC;AAClC,sBAA+H;AAC/H,mBAAoC;AACpC,mBAAwB;AAExB,0BAA+B;AAC/B,yBAA2B;AAsDjB;AApDH,SAAS,qBAAqB;AACnC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,QAAQ,CAAC;AACpC,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,IAAI;AAC/C,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,KAAK;AACxD,QAAM,CAAC,UAAU,WAAW,QAAI,uBAA0B,CAAC,CAAC;AAC5D,QAAM,CAAC,sBAAsB,uBAAuB,QAAI,uBAAS,KAAK;AAGtE,8BAAU,MAAM;AACd,2CAAkB,YAAY;AAC5B,mBAAa,IAAI;AACjB,YAAM,eAAe,MAAM,KAAK,kBAAkB;AAClD,YAAM,mBAAmB;AACzB,kBAAY,gBAAgB;AAC5B,mBAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH,GAAG,CAAC,IAAI,CAAC;AAET,QAAM,sBAAsB,OAAO,cAAsB;AACvD,QAAI;AACF,YAAM,KAAK,cAAc,SAAS;AAGlC,kBAAY,UAAQ,KAAK,OAAO,aAAW,QAAQ,OAAO,SAAS,CAAC;AAAA,IACtE,SAAS,OAAO;AACd,sCAAa,4BAA4B,EAAE,WAAW,MAAM,CAAC;AAC7D,YAAM;AAAA,IACR;AAAA,EACF;AAEA,QAAM,0BAA0B,YAAY;AAC1C,qBAAiB,IAAI;AACrB,QAAI;AACF,YAAM,mBAAmB,SACtB,OAAO,aAAW,CAAC,QAAQ,gBAAgB,EAC3C,IAAI,aAAW,KAAK,cAAc,QAAQ,EAAE,CAAC;AAChD,YAAM,QAAQ,IAAI,gBAAgB;AAClC,kBAAY,kBAAgB,aAAa,OAAO,aAAW,QAAQ,gBAAgB,CAAC;AAAA,IACtF,SAAS,OAAO;AACd,sCAAa,iCAAiC,EAAE,OAAO,YAAY,SAAS,IAAI,aAAW,QAAQ,EAAE,EAAE,CAAC;AACxG,YAAM;AAAA,IACR,UAAE;AACA,uBAAiB,KAAK;AACtB,8BAAwB,KAAK;AAAA,IAC/B;AAAA,EACF;AAEA,SACE,4CAAC,iCACC,uDAAC,SACC;AAAA,iDAAC,SAAI,WAAU,0CACb;AAAA,kDAAC,8BAAW,WAAU,eAAe,YAAE,iBAAiB,GAAE;AAAA,MACzD,SAAS,OAAO,OAAK,CAAC,EAAE,gBAAgB,EAAE,SAAS,KAAK,CAAC,cACxD,uBACE,6CAAC,SAAI,WAAU,cACb;AAAA;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,YAER,YAAE,SAAS;AAAA;AAAA,QACd;AAAA,QACA;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,UAAU;AAAA,YACV,SAAS,MAAM,wBAAwB,KAAK;AAAA,YAE3C,YAAE,QAAQ;AAAA;AAAA,QACb;AAAA,SACF,IAEA;AAAA,QAAC;AAAA;AAAA,UACC,SAAQ;AAAA,UACR,MAAK;AAAA,UACL,SAAS,MAAM,wBAAwB,IAAI;AAAA,UAE1C,YAAE,2BAA2B;AAAA;AAAA,MAChC;AAAA,OAGN;AAAA,IACA,4CAAC,8BAAW,SAAQ,aAAY,MAAK,YAAW,WAAU,QACvD,YAAE,6FAA6F,GAClG;AAAA,IAEC,YACC,4CAAC,4BAAS,WAAU,+BAA8B,IAElD,4CAAC,SAAI,WAAU,qBACb,uDAAC,yBACC;AAAA,kDAAC,+BACC,uDAAC,4BACC;AAAA,oDAAC,6BAAU,WAAU,aAAa,YAAE,SAAS,GAAE;AAAA,QAC/C,4CAAC,6BAAU,WAAU,aAAa,YAAE,YAAY,GAAE;AAAA,QAClD,4CAAC,6BAAU,WAAU,aAAa,YAAE,UAAU,GAAE;AAAA,QAChD,4CAAC,6BAAU,WAAU,aAAa,YAAE,WAAW,GAAE;AAAA,QACjD,4CAAC,6BAAU,WAAU,YAAW;AAAA,SAClC,GACF;AAAA,MACA,4CAAC,6BACE,mBAAS,WAAW,IACnB,4CAAC,4BACC,sDAAC,6BAAU,SAAS,GAAG,WAAU,oBAC/B,sDAAC,8BAAW,SAAQ,aAAa,YAAE,0BAA0B,GAAE,GACjE,GACF,IAEA,SAAS,IAAI,CAAC,YACZ,6CAAC,4BACC;AAAA,oDAAC,6BACC,uDAAC,SAAI,WAAU,iBAEb;AAAA,sDAAC,8BAAY,kBAAQ,mBAAmB,EAAE,iBAAiB,IAAI,EAAE,eAAe,GAAE;AAAA,UACjF,QAAQ,mBAAmB,4CAAC,yBAAM,SAAQ,aAAY,WAAU,cAAc,YAAE,eAAe,GAAE;AAAA,UAClG,4CAAC,8BAAW,SAAQ,aAAY,MAAK,YAClC,YAAE,oBAAoB,EAAE,MAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,mBAAmB,EAAE,CAAC,GACnF;AAAA,WACF,GACF;AAAA,QACA,4CAAC,6BACC,sDAAC,8BAAY,kBAAQ,SAAS,MAAM,EAAE,GAAG,GAAE,GAC7C;AAAA,QACA,4CAAC,6BACC,sDAAC,8BAAY,kBAAQ,SAAS,YAAY,EAAE,SAAS,GAAE,GACzD;AAAA,QACA,4CAAC,6BACC,uDAAC,SAAI,WAAU,iBACb;AAAA,sDAAC,8BAAY,kBAAQ,iBAAa,sBAAQ,IAAI,KAAK,QAAQ,UAAU,CAAC,IAAI,EAAE,OAAO,GAAE;AAAA,UACrF,4CAAC,8BAAW,SAAQ,aAAY,MAAK,YAAW,OAAO,QAAQ,aAAa,IAAI,KAAK,QAAQ,UAAU,EAAE,eAAe,IAAI,IACzH,kBAAQ,aAAa,IAAI,KAAK,QAAQ,UAAU,EAAE,mBAAmB,IAAI,IAC5E;AAAA,WACF,GACF;AAAA,QACA,4CAAC,6BAAU,OAAM,SACf;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,cACL;AAAA,gBACE,MAAM,EAAE,QAAQ;AAAA,gBAChB,SAAS,MAAM,oBAAoB,QAAQ,EAAE;AAAA,gBAC7C,QAAQ;AAAA,gBACR,UAAU,QAAQ;AAAA,gBAClB,iBAAiB,QAAQ,mBAAmB,EAAE,wCAAwC,IAAI;AAAA,cAC5F;AAAA,YACF;AAAA;AAAA,QACF,GACF;AAAA,WArCa,QAAQ,EAsCvB,CACD,GAEL;AAAA,OACF,GACF;AAAA,KAEJ,GACF;AAEJ;", "names": []}