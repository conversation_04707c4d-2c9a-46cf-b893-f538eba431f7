{"version": 3, "sources": ["../../../src/components/elements/user-avatar.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Avatar, AvatarFallback, AvatarImage } from \"@stackframe/stack-ui\";\nimport { UserRound } from \"lucide-react\";\n\nconst defaultSize = 34;\n\nexport function UserAvatar(props: {\n  size?: number,\n  user?: {\n    profileImageUrl?: string | null,\n    displayName?: string | null,\n    primaryEmail?: string | null,\n  } | null,\n  border?: boolean,\n}) {\n  const user = props.user;\n  return (\n    <Avatar style={{ height: props.size || defaultSize, width: props.size || defaultSize }} className={props.border ? 'border' : ''}>\n      <AvatarImage src={user?.profileImageUrl || ''} />\n      <AvatarFallback>\n        {user ?\n          <div className='font-medium' style={{ fontSize: (props.size || defaultSize) * 0.4 }}>\n            {(user.displayName || user.primaryEmail)?.slice(0, 2).toUpperCase()}\n          </div> :\n          <UserRound className=\"text-zinc-500\" size={(props.size || defaultSize) * 0.6} />}\n      </AvatarFallback>\n    </Avatar>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAAoD;AACpD,0BAA0B;AAetB;AAbJ,IAAM,cAAc;AAEb,SAAS,WAAW,OAQxB;AACD,QAAM,OAAO,MAAM;AACnB,SACE,6CAAC,0BAAO,OAAO,EAAE,QAAQ,MAAM,QAAQ,aAAa,OAAO,MAAM,QAAQ,YAAY,GAAG,WAAW,MAAM,SAAS,WAAW,IAC3H;AAAA,gDAAC,+BAAY,KAAK,MAAM,mBAAmB,IAAI;AAAA,IAC/C,4CAAC,kCACE,iBACC,4CAAC,SAAI,WAAU,eAAc,OAAO,EAAE,WAAW,MAAM,QAAQ,eAAe,IAAI,GAC9E,gBAAK,eAAe,KAAK,eAAe,MAAM,GAAG,CAAC,EAAE,YAAY,GACpE,IACA,4CAAC,iCAAU,WAAU,iBAAgB,OAAO,MAAM,QAAQ,eAAe,KAAK,GAClF;AAAA,KACF;AAEJ;", "names": []}