"use client";
"use strict";
"use client";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/elements/separator-with-text.tsx
var separator_with_text_exports = {};
__export(separator_with_text_exports, {
  SeparatorWithText: () => SeparatorWithText
});
module.exports = __toCommonJS(separator_with_text_exports);
var import_stack_ui = require("@stackframe/stack-ui");
var import_jsx_runtime = require("react/jsx-runtime");
function SeparatorWithText({ text }) {
  return /* @__PURE__ */ (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-center my-6 stack-scope", children: [
    /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { className: "flex-1", children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_stack_ui.Separator, {}) }),
    /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { className: "mx-2 text-sm text-zinc-500", children: text }),
    /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { className: "flex-1", children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_stack_ui.Separator, {}) })
  ] });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  SeparatorWithText
});
//# sourceMappingURL=separator-with-text.js.map