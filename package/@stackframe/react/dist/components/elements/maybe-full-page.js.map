{"version": 3, "sources": ["../../../src/components/elements/maybe-full-page.tsx"], "sourcesContent": ["\"use client\";\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport React, { useId } from \"react\";\nimport { SsrScript } from \"./ssr-layout-effect\";\n\nexport function MaybeFullPage({\n  children,\n  fullPage,\n}: {\n  children: React.ReactNode,\n  fullPage: boolean,\n  size?: number,\n  containerClassName?: string,\n}) {\n  const uniqueId = useId();\n  const id = `stack-full-page-container-${uniqueId}`;\n\n  const scriptString = `(([id]) => {\n    const el = document.getElementById(id);\n    if (!el) {\n      // component is not full page\n      return;\n    }\n    const offset = el.getBoundingClientRect().top + document.documentElement.scrollTop;\n    el.style.minHeight = \\`calc(100vh - \\${offset}px)\\`;\n  })(${JSON.stringify([id])})`;\n\n  if (fullPage) {\n    return (\n      <>\n        <div\n          suppressHydrationWarning\n          id={id}\n          style={{\n            minHeight: '100vh',\n            alignSelf: 'stretch',\n            flexGrow: 1,\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n          }}\n          className=\"stack-scope\"\n        >\n          {children}\n        </div>\n        <SsrScript script={scriptString} />\n      </>\n    );\n  } else {\n    return <>\n      {children}\n    </>;\n  }\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,mBAA6B;AAC7B,+BAA0B;AA0BpB;AAxBC,SAAS,cAAc;AAAA,EAC5B;AAAA,EACA;AACF,GAKG;AACD,QAAM,eAAW,oBAAM;AACvB,QAAM,KAAK,6BAA6B,QAAQ;AAEhD,QAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAQhB,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC;AAEzB,MAAI,UAAU;AACZ,WACE,4EACE;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,0BAAwB;AAAA,UACxB;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,YACX,WAAW;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,YACT,gBAAgB;AAAA,YAChB,YAAY;AAAA,UACd;AAAA,UACA,WAAU;AAAA,UAET;AAAA;AAAA,MACH;AAAA,MACA,4CAAC,sCAAU,QAAQ,cAAc;AAAA,OACnC;AAAA,EAEJ,OAAO;AACL,WAAO,2EACJ,UACH;AAAA,EACF;AAEF;", "names": []}