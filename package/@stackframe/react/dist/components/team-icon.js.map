{"version": 3, "sources": ["../../src/components/team-icon.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Avatar, AvatarImage, Typography } from \"@stackframe/stack-ui\";\nimport { Team } from \"..\";\n\nexport function TeamIcon(props: { team: Team }) {\n  if (props.team.profileImageUrl) {\n    return (\n      <Avatar className=\"min-w-6 min-h-6 max-w-6 max-h-6 rounded\">\n        <AvatarImage src={props.team.profileImageUrl} alt={props.team.displayName} />\n      </Avatar>\n    );\n  } else {\n    return (\n      <div className=\"flex items-center justify-center min-w-6 min-h-6 max-w-6 max-h-6 rounded bg-zinc-200\">\n        <Typography className=\"text-zinc-800 dark:text-zinc-800\">{props.team.displayName.slice(0, 1).toUpperCase()}</Typography>\n      </div>\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAAgD;AAOxC;AAJD,SAAS,SAAS,OAAuB;AAC9C,MAAI,MAAM,KAAK,iBAAiB;AAC9B,WACE,4CAAC,0BAAO,WAAU,2CAChB,sDAAC,+BAAY,KAAK,MAAM,KAAK,iBAAiB,KAAK,MAAM,KAAK,aAAa,GAC7E;AAAA,EAEJ,OAAO;AACL,WACE,4CAAC,SAAI,WAAU,wFACb,sDAAC,8BAAW,WAAU,oCAAoC,gBAAM,KAAK,YAAY,MAAM,GAAG,CAAC,EAAE,YAAY,GAAE,GAC7G;AAAA,EAEJ;AACF;", "names": []}