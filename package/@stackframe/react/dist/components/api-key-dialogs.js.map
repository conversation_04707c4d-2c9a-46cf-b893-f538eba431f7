{"version": 3, "sources": ["../../src/components/api-key-dialogs.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { yupObject, yupString } from '@stackframe/stack-shared/dist/schema-fields';\nimport { captureError } from '@stackframe/stack-shared/dist/utils/errors';\nimport { runAsynchronously } from '@stackframe/stack-shared/dist/utils/promises';\nimport { ActionDialog, Button, CopyField, Input, Label, Typography } from '@stackframe/stack-ui';\nimport { useState } from \"react\";\nimport { useForm } from 'react-hook-form';\nimport * as yup from \"yup\";\nimport { useUser } from '..';\nimport { FormWarningText } from '../components/elements/form-warning';\nimport { ApiK<PERSON>, ApiKeyCreationOptions, ApiKeyType } from \"../lib/stack-app/api-keys\";\nimport { useTranslation } from \"../lib/translations\";\n\n// Constants for expiration options\nexport const neverInMs = 1000 * 60 * 60 * 24 * 365 * 200;\nexport const expiresInOptions = {\n  [1000 * 60 * 60 * 24 * 1]: \"1 day\",\n  [1000 * 60 * 60 * 24 * 7]: \"7 days\",\n  [1000 * 60 * 60 * 24 * 30]: \"30 days\",\n  [1000 * 60 * 60 * 24 * 90]: \"90 days\",\n  [1000 * 60 * 60 * 24 * 365]: \"1 year\",\n  [neverInMs]: \"Never\",\n} as const;\n\n/**\n * Dialog for creating a new API key\n */\nexport function CreateApiKeyDialog<Type extends ApiKeyType = ApiKeyType>(props: {\n  open: boolean,\n  onOpenChange: (open: boolean) => void,\n  onKeyCreated?: (key: ApiKey<Type, true>) => void,\n  createApiKey: (data: ApiKeyCreationOptions<Type>) => Promise<ApiKey<Type, true>>,\n}) {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const [loading, setLoading] = useState(false);\n\n  const apiKeySchema = yupObject({\n    description: yupString().defined().nonEmpty(t('Description is required')),\n    expiresIn: yupString().defined(),\n  });\n\n  const { register, handleSubmit, formState: { errors }, reset } = useForm({\n    resolver: yupResolver(apiKeySchema),\n    defaultValues: {\n      description: '',\n      expiresIn: Object.keys(expiresInOptions)[2], // Default to 30 days\n    }\n  });\n\n  const onSubmit = async (data: yup.InferType<typeof apiKeySchema>) => {\n    setLoading(true);\n    try {\n      const expiresAt = new Date(Date.now() + parseInt(data.expiresIn));\n      const apiKey = await props.createApiKey({\n        description: data.description,\n        expiresAt,\n      });\n\n      if (props.onKeyCreated) {\n        props.onKeyCreated(apiKey);\n      }\n\n      reset();\n      props.onOpenChange(false);\n    } catch (error) {\n      captureError(\"Failed to create API key\", { error });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <ActionDialog\n      open={props.open}\n      onOpenChange={props.onOpenChange}\n      title={t('Create API Key')}\n      description={t('API keys grant programmatic access to your account.')}\n    >\n      <form\n        onSubmit={(e) => {\n          e.preventDefault();\n          runAsynchronously(handleSubmit(onSubmit));\n        }}\n        className=\"space-y-4\"\n      >\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"description\">{t('Description')}</Label>\n          <Input\n            id=\"description\"\n            placeholder={t('e.g. Development, Production, CI/CD')}\n            {...register('description')}\n          />\n          {errors.description && <FormWarningText text={errors.description.message} />}\n        </div>\n\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"expiresIn\">{t('Expires In')}</Label>\n          <select\n            id=\"expiresIn\"\n            className=\"w-full p-2 border border-input rounded-md bg-background\"\n            {...register('expiresIn')}\n          >\n            {Object.entries(expiresInOptions).map(([value, label]) => (\n              <option key={value} value={value}>{t(label)}</option>\n            ))}\n          </select>\n          {errors.expiresIn && <FormWarningText text={errors.expiresIn.message} />}\n        </div>\n\n        <div className=\"flex justify-end gap-2 pt-4\">\n          <Button\n            type=\"button\"\n            variant=\"secondary\"\n            onClick={() => {\n              reset();\n              props.onOpenChange(false);\n            }}\n          >\n            {t('Cancel')}\n          </Button>\n          <Button type=\"submit\" loading={loading}>\n            {t('Create')}\n          </Button>\n        </div>\n      </form>\n    </ActionDialog>\n  );\n}\n\n/**\n * Dialog for showing the newly created API key\n */\nexport function ShowApiKeyDialog<Type extends ApiKeyType = ApiKeyType>(props: {\n  apiKey: ApiKey<Type, true> | null,\n  onClose?: () => void,\n}) {\n  const { t } = useTranslation();\n\n  return (\n    <ActionDialog\n      open={!!props.apiKey}\n      title={t(\"API Key\")}\n      okButton={{ label: t(\"Close\") }}\n      onClose={props.onClose}\n      preventClose\n      confirmText={t(\"I understand that I will not be able to view this key again.\")}\n    >\n      <div className=\"flex flex-col gap-4\">\n        <Typography>\n          {t(\"Here is your API key.\")}{\" \"}\n          <span className=\"font-bold\">\n            {t(\"Copy it to a safe place. You will not be able to view it again.\")}\n          </span>\n        </Typography>\n        <CopyField\n          monospace\n          value={props.apiKey?.value ?? ''}\n          label={t(\"Secret API Key\")}\n        />\n      </div>\n    </ActionDialog>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,iBAA4B;AAC5B,2BAAqC;AACrC,oBAA6B;AAC7B,sBAAkC;AAClC,sBAA0E;AAC1E,mBAAyB;AACzB,6BAAwB;AAExB,eAAwB;AACxB,0BAAgC;AAEhC,0BAA+B;AA2EvB;AAxED,IAAM,YAAY,MAAO,KAAK,KAAK,KAAK,MAAM;AAC9C,IAAM,mBAAmB;AAAA,EAC9B,CAAC,MAAO,KAAK,KAAK,KAAK,CAAC,GAAG;AAAA,EAC3B,CAAC,MAAO,KAAK,KAAK,KAAK,CAAC,GAAG;AAAA,EAC3B,CAAC,MAAO,KAAK,KAAK,KAAK,EAAE,GAAG;AAAA,EAC5B,CAAC,MAAO,KAAK,KAAK,KAAK,EAAE,GAAG;AAAA,EAC5B,CAAC,MAAO,KAAK,KAAK,KAAK,GAAG,GAAG;AAAA,EAC7B,CAAC,SAAS,GAAG;AACf;AAKO,SAAS,mBAAyD,OAKtE;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,kBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAE5C,QAAM,mBAAe,gCAAU;AAAA,IAC7B,iBAAa,gCAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,yBAAyB,CAAC;AAAA,IACxE,eAAW,gCAAU,EAAE,QAAQ;AAAA,EACjC,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,WAAW,EAAE,OAAO,GAAG,MAAM,QAAI,gCAAQ;AAAA,IACvE,cAAU,wBAAY,YAAY;AAAA,IAClC,eAAe;AAAA,MACb,aAAa;AAAA,MACb,WAAW,OAAO,KAAK,gBAAgB,EAAE,CAAC;AAAA;AAAA,IAC5C;AAAA,EACF,CAAC;AAED,QAAM,WAAW,OAAO,SAA6C;AACnE,eAAW,IAAI;AACf,QAAI;AACF,YAAM,YAAY,IAAI,KAAK,KAAK,IAAI,IAAI,SAAS,KAAK,SAAS,CAAC;AAChE,YAAM,SAAS,MAAM,MAAM,aAAa;AAAA,QACtC,aAAa,KAAK;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,MAAM,cAAc;AACtB,cAAM,aAAa,MAAM;AAAA,MAC3B;AAEA,YAAM;AACN,YAAM,aAAa,KAAK;AAAA,IAC1B,SAAS,OAAO;AACd,sCAAa,4BAA4B,EAAE,MAAM,CAAC;AAAA,IACpD,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,MAAM,MAAM;AAAA,MACZ,cAAc,MAAM;AAAA,MACpB,OAAO,EAAE,gBAAgB;AAAA,MACzB,aAAa,EAAE,qDAAqD;AAAA,MAEpE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,CAAC,MAAM;AACf,cAAE,eAAe;AACjB,mDAAkB,aAAa,QAAQ,CAAC;AAAA,UAC1C;AAAA,UACA,WAAU;AAAA,UAEV;AAAA,yDAAC,SAAI,WAAU,aACb;AAAA,0DAAC,yBAAM,SAAQ,eAAe,YAAE,aAAa,GAAE;AAAA,cAC/C;AAAA,gBAAC;AAAA;AAAA,kBACC,IAAG;AAAA,kBACH,aAAa,EAAE,qCAAqC;AAAA,kBACnD,GAAG,SAAS,aAAa;AAAA;AAAA,cAC5B;AAAA,cACC,OAAO,eAAe,4CAAC,uCAAgB,MAAM,OAAO,YAAY,SAAS;AAAA,eAC5E;AAAA,YAEA,6CAAC,SAAI,WAAU,aACb;AAAA,0DAAC,yBAAM,SAAQ,aAAa,YAAE,YAAY,GAAE;AAAA,cAC5C;AAAA,gBAAC;AAAA;AAAA,kBACC,IAAG;AAAA,kBACH,WAAU;AAAA,kBACT,GAAG,SAAS,WAAW;AAAA,kBAEvB,iBAAO,QAAQ,gBAAgB,EAAE,IAAI,CAAC,CAAC,OAAO,KAAK,MAClD,4CAAC,YAAmB,OAAe,YAAE,KAAK,KAA7B,KAA+B,CAC7C;AAAA;AAAA,cACH;AAAA,cACC,OAAO,aAAa,4CAAC,uCAAgB,MAAM,OAAO,UAAU,SAAS;AAAA,eACxE;AAAA,YAEA,6CAAC,SAAI,WAAU,+BACb;AAAA;AAAA,gBAAC;AAAA;AAAA,kBACC,MAAK;AAAA,kBACL,SAAQ;AAAA,kBACR,SAAS,MAAM;AACb,0BAAM;AACN,0BAAM,aAAa,KAAK;AAAA,kBAC1B;AAAA,kBAEC,YAAE,QAAQ;AAAA;AAAA,cACb;AAAA,cACA,4CAAC,0BAAO,MAAK,UAAS,SACnB,YAAE,QAAQ,GACb;AAAA,eACF;AAAA;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ;AAKO,SAAS,iBAAuD,OAGpE;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,SACE;AAAA,IAAC;AAAA;AAAA,MACC,MAAM,CAAC,CAAC,MAAM;AAAA,MACd,OAAO,EAAE,SAAS;AAAA,MAClB,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE;AAAA,MAC9B,SAAS,MAAM;AAAA,MACf,cAAY;AAAA,MACZ,aAAa,EAAE,8DAA8D;AAAA,MAE7E,uDAAC,SAAI,WAAU,uBACb;AAAA,qDAAC,8BACE;AAAA,YAAE,uBAAuB;AAAA,UAAG;AAAA,UAC7B,4CAAC,UAAK,WAAU,aACb,YAAE,iEAAiE,GACtE;AAAA,WACF;AAAA,QACA;AAAA,UAAC;AAAA;AAAA,YACC,WAAS;AAAA,YACT,OAAO,MAAM,QAAQ,SAAS;AAAA,YAC9B,OAAO,EAAE,gBAAgB;AAAA;AAAA,QAC3B;AAAA,SACF;AAAA;AAAA,EACF;AAEJ;", "names": []}