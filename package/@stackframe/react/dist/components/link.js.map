{"version": 3, "sources": ["../../src/components/link.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { cn } from \"@stackframe/stack-ui\";\n\ntype LinkProps = {\n  href: string,\n  children: React.ReactNode,\n  className?: string,\n  target?: string,\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>,\n  prefetch?: boolean,\n};\n\nfunction Link(props: LinkProps) {\n  return <a\n    href={props.href}\n    target={props.target}\n    className={props.className}\n    onClick={props.onClick}\n  >\n    {props.children}\n  </a>;\n}\n\nfunction StyledLink(props: LinkProps) {\n  return (\n    <Link {...props} className={cn(\"underline font-medium\", props.className)}>\n      {props.children}\n    </Link>\n  );\n}\n\nexport { Link, StyledLink };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAAmB;AAYV;AADT,SAAS,KAAK,OAAkB;AAC9B,SAAO;AAAA,IAAC;AAAA;AAAA,MACN,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM;AAAA,MACd,WAAW,MAAM;AAAA,MACjB,SAAS,MAAM;AAAA,MAEd,gBAAM;AAAA;AAAA,EACT;AACF;AAEA,SAAS,WAAW,OAAkB;AACpC,SACE,4CAAC,QAAM,GAAG,OAAO,eAAW,oBAAG,yBAAyB,MAAM,SAAS,GACpE,gBAAM,UACT;AAEJ;", "names": []}