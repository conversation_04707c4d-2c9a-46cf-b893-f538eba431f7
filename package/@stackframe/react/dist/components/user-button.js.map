{"version": 3, "sources": ["../../src/components/user-button.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, Skeleton, Typography } from \"@stackframe/stack-ui\";\nimport { CircleUser, LogIn, LogOut, SunMoon, UserPlus } from \"lucide-react\";\nimport React, { Suspense } from \"react\";\nimport { CurrentUser, useStackApp, useUser } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { UserAvatar } from \"./elements/user-avatar\";\n\nfunction Item(props: { text: string, icon: React.ReactNode, onClick: () => void | Promise<void> }) {\n  return (\n    <DropdownMenuItem onClick={() => runAsynchronouslyWithAlert(props.onClick)}>\n      <div className=\"flex gap-2 items-center\">\n        {props.icon}\n        <Typography>{props.text}</Typography>\n      </div>\n    </DropdownMenuItem>\n  );\n}\n\ntype UserButtonProps = {\n  showUserInfo?: boolean,\n  colorModeToggle?: () => void | Promise<void>,\n  extraItems?: {\n    text: string,\n    icon: React.ReactNode,\n    onClick: () => void | Promise<void>,\n  }[],\n};\n\nexport function UserButton(props: UserButtonProps) {\n  return (\n    <Suspense fallback={<Skeleton className=\"h-[34px] w-[34px] rounded-full stack-scope\" />}>\n      <UserButtonInner {...props} />\n    </Suspense>\n  );\n}\n\nfunction UserButtonInner(props: UserButtonProps) {\n  const user = useUser();\n  return <UserButtonInnerInner {...props} user={user} />;\n}\n\n\nfunction UserButtonInnerInner(props: UserButtonProps & { user: CurrentUser | null }) {\n  const { t } = useTranslation();\n  const user = props.user;\n  const app = useStackApp();\n\n  const iconProps = { size: 20, className: 'h-4 w-4' };\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger className=\"outline-none stack-scope\">\n        <div className=\"flex gap-2 items-center\">\n          <UserAvatar user={user} />\n          {user && props.showUserInfo &&\n            <div className=\"flex flex-col justify-center text-left\">\n              <Typography className=\"max-w-40 truncate\">{user.displayName}</Typography>\n              <Typography className=\"max-w-40 truncate\" variant=\"secondary\" type='label'>{user.primaryEmail}</Typography>\n            </div>\n          }\n        </div>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"stack-scope\">\n        <DropdownMenuLabel>\n          <div className=\"flex gap-2 items-center\">\n            <UserAvatar user={user} />\n            <div>\n              {user && <Typography className=\"max-w-40 truncate\">{user.displayName}</Typography>}\n              {user && <Typography className=\"max-w-40 truncate\" variant=\"secondary\" type='label'>{user.primaryEmail}</Typography>}\n              {!user && <Typography>{t('Not signed in')}</Typography>}\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {user && <Item\n          text={t('Account settings')}\n          onClick={async () => await app.redirectToAccountSettings()}\n          icon={<CircleUser {...iconProps} />}\n        />}\n        {!user && <Item\n          text={t('Sign in')}\n          onClick={async () => await app.redirectToSignIn()}\n          icon={<LogIn {...iconProps} />}\n        />}\n        {!user && <Item\n          text={t('Sign up')}\n          onClick={async () => await app.redirectToSignUp()}\n          icon={<UserPlus {...iconProps}/> }\n        />}\n        {user && props.extraItems && props.extraItems.map((item, index) => (\n          <Item key={index} {...item} />\n        ))}\n        {props.colorModeToggle && (\n          <Item\n            text={t('Toggle theme')}\n            onClick={props.colorModeToggle}\n            icon={<SunMoon {...iconProps} />}\n          />\n        )}\n        {user && <Item\n          text={t('Sign out')}\n          onClick={() => user.signOut()}\n          icon={<LogOut {...iconProps} />}\n        />}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAA2C;AAC3C,sBAAyJ;AACzJ,0BAA6D;AAC7D,mBAAgC;AAChC,eAAkD;AAClD,0BAA+B;AAC/B,yBAA2B;AAKrB;AAHN,SAAS,KAAK,OAAqF;AACjG,SACE,4CAAC,oCAAiB,SAAS,UAAM,4CAA2B,MAAM,OAAO,GACvE,uDAAC,SAAI,WAAU,2BACZ;AAAA,UAAM;AAAA,IACP,4CAAC,8BAAY,gBAAM,MAAK;AAAA,KAC1B,GACF;AAEJ;AAYO,SAAS,WAAW,OAAwB;AACjD,SACE,4CAAC,yBAAS,UAAU,4CAAC,4BAAS,WAAU,8CAA6C,GACnF,sDAAC,mBAAiB,GAAG,OAAO,GAC9B;AAEJ;AAEA,SAAS,gBAAgB,OAAwB;AAC/C,QAAM,WAAO,kBAAQ;AACrB,SAAO,4CAAC,wBAAsB,GAAG,OAAO,MAAY;AACtD;AAGA,SAAS,qBAAqB,OAAuD;AACnF,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,OAAO,MAAM;AACnB,QAAM,UAAM,sBAAY;AAExB,QAAM,YAAY,EAAE,MAAM,IAAI,WAAW,UAAU;AAEnD,SACE,6CAAC,gCACC;AAAA,gDAAC,uCAAoB,WAAU,4BAC7B,uDAAC,SAAI,WAAU,2BACb;AAAA,kDAAC,iCAAW,MAAY;AAAA,MACvB,QAAQ,MAAM,gBACb,6CAAC,SAAI,WAAU,0CACb;AAAA,oDAAC,8BAAW,WAAU,qBAAqB,eAAK,aAAY;AAAA,QAC5D,4CAAC,8BAAW,WAAU,qBAAoB,SAAQ,aAAY,MAAK,SAAS,eAAK,cAAa;AAAA,SAChG;AAAA,OAEJ,GACF;AAAA,IACA,6CAAC,uCAAoB,WAAU,eAC7B;AAAA,kDAAC,qCACC,uDAAC,SAAI,WAAU,2BACb;AAAA,oDAAC,iCAAW,MAAY;AAAA,QACxB,6CAAC,SACE;AAAA,kBAAQ,4CAAC,8BAAW,WAAU,qBAAqB,eAAK,aAAY;AAAA,UACpE,QAAQ,4CAAC,8BAAW,WAAU,qBAAoB,SAAQ,aAAY,MAAK,SAAS,eAAK,cAAa;AAAA,UACtG,CAAC,QAAQ,4CAAC,8BAAY,YAAE,eAAe,GAAE;AAAA,WAC5C;AAAA,SACF,GACF;AAAA,MACA,4CAAC,yCAAsB;AAAA,MACtB,QAAQ;AAAA,QAAC;AAAA;AAAA,UACR,MAAM,EAAE,kBAAkB;AAAA,UAC1B,SAAS,YAAY,MAAM,IAAI,0BAA0B;AAAA,UACzD,MAAM,4CAAC,kCAAY,GAAG,WAAW;AAAA;AAAA,MACnC;AAAA,MACC,CAAC,QAAQ;AAAA,QAAC;AAAA;AAAA,UACT,MAAM,EAAE,SAAS;AAAA,UACjB,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAAA,UAChD,MAAM,4CAAC,6BAAO,GAAG,WAAW;AAAA;AAAA,MAC9B;AAAA,MACC,CAAC,QAAQ;AAAA,QAAC;AAAA;AAAA,UACT,MAAM,EAAE,SAAS;AAAA,UACjB,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAAA,UAChD,MAAM,4CAAC,gCAAU,GAAG,WAAU;AAAA;AAAA,MAChC;AAAA,MACC,QAAQ,MAAM,cAAc,MAAM,WAAW,IAAI,CAAC,MAAM,UACvD,4CAAC,QAAkB,GAAG,QAAX,KAAiB,CAC7B;AAAA,MACA,MAAM,mBACL;AAAA,QAAC;AAAA;AAAA,UACC,MAAM,EAAE,cAAc;AAAA,UACtB,SAAS,MAAM;AAAA,UACf,MAAM,4CAAC,+BAAS,GAAG,WAAW;AAAA;AAAA,MAChC;AAAA,MAED,QAAQ;AAAA,QAAC;AAAA;AAAA,UACR,MAAM,EAAE,UAAU;AAAA,UAClB,SAAS,MAAM,KAAK,QAAQ;AAAA,UAC5B,MAAM,4CAAC,8BAAQ,GAAG,WAAW;AAAA;AAAA,MAC/B;AAAA,OACF;AAAA,KACF;AAEJ;", "names": []}