{"version": 3, "sources": ["../../src/components/iframe-preventer.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { useEffect, useState } from \"react\";\n\nexport function IframePreventer({ children }: {\n  children: React.ReactNode,\n}) {\n  const [isIframe, setIsIframe] = useState(false);\n  useEffect(() => {\n    if (window.self !== window.top) {\n      setIsIframe(true);\n    }\n  }, []);\n\n  if (isIframe) {\n    return <div>Stack Auth components may not run in an {'<'}iframe{'>'}.</div>;\n  }\n\n  return children;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,mBAAoC;AAazB;AAXJ,SAAS,gBAAgB,EAAE,SAAS,GAExC;AACD,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,KAAK;AAC9C,8BAAU,MAAM;AACd,QAAI,OAAO,SAAS,OAAO,KAAK;AAC9B,kBAAY,IAAI;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,MAAI,UAAU;AACZ,WAAO,6CAAC,SAAI;AAAA;AAAA,MAAyC;AAAA,MAAI;AAAA,MAAO;AAAA,MAAI;AAAA,OAAC;AAAA,EACvE;AAEA,SAAO;AACT;", "names": []}