{"version": 3, "sources": ["../../src/components/credential-sign-in.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { passwordSchema, strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Label, PasswordInput } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { useStackApp } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { FormWarningText } from \"./elements/form-warning\";\nimport { StyledLink } from \"./link\";\n\nexport function CredentialSignIn() {\n  const { t } = useTranslation();\n\n  const schema = yupObject({\n    email: strictEmailSchema(t('Please enter a valid email')).defined().nonEmpty(t('Please enter your email')),\n    password: passwordSchema.defined().nonEmpty(t('Please enter your password'))\n  });\n\n  const { register, handleSubmit, setError, formState: { errors } } = useForm({\n    resolver: yupResolver(schema)\n  });\n  const app = useStackApp();\n  const [loading, setLoading] = useState(false);\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n\n    try {\n      const { email, password } = data;\n      const result = await app.signInWithCredential({\n        email,\n        password,\n      });\n      if (result.status === 'error') {\n        setError('email', { type: 'manual', message: result.error.message });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form\n      className=\"flex flex-col items-stretch stack-scope\"\n      onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n      noValidate\n    >\n      <Label htmlFor=\"email\" className=\"mb-1\">{t('Email')}</Label>\n      <Input\n        id=\"email\"\n        type=\"email\"\n        autoComplete=\"email\"\n        {...register('email')}\n      />\n      <FormWarningText text={errors.email?.message?.toString()} />\n\n      <Label htmlFor=\"password\" className=\"mt-4 mb-1\">{t('Password')}</Label>\n      <PasswordInput\n        id=\"password\"\n        autoComplete=\"current-password\"\n        {...register('password')}\n      />\n      <FormWarningText text={errors.password?.message?.toString()} />\n\n      <StyledLink href={app.urls.forgotPassword} className=\"mt-1 text-sm\">\n        {t('Forgot password?')}\n      </StyledLink>\n\n      <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n        {t('Sign In')}\n      </Button>\n    </form>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,iBAA4B;AAC5B,2BAA6D;AAC7D,sBAA2C;AAC3C,sBAAoD;AACpD,mBAAyB;AACzB,6BAAwB;AAExB,eAA4B;AAC5B,0BAA+B;AAC/B,0BAAgC;AAChC,kBAA2B;AAkCvB;AAhCG,SAAS,mBAAmB;AACjC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,QAAM,aAAS,gCAAU;AAAA,IACvB,WAAO,wCAAkB,EAAE,4BAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,yBAAyB,CAAC;AAAA,IACzG,UAAU,oCAAe,QAAQ,EAAE,SAAS,EAAE,4BAA4B,CAAC;AAAA,EAC7E,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,UAAU,WAAW,EAAE,OAAO,EAAE,QAAI,gCAAQ;AAAA,IAC1E,cAAU,wBAAY,MAAM;AAAA,EAC9B,CAAC;AACD,QAAM,UAAM,sBAAY;AACxB,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAE5C,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AAEf,QAAI;AACF,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,SAAS,MAAM,IAAI,qBAAqB;AAAA,QAC5C;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,OAAO,WAAW,SAAS;AAC7B,iBAAS,SAAS,EAAE,MAAM,UAAU,SAAS,OAAO,MAAM,QAAQ,CAAC;AAAA,MACrE;AAAA,IACF,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,UAAU,WAAK,4CAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,MACnE,YAAU;AAAA,MAEV;AAAA,oDAAC,yBAAM,SAAQ,SAAQ,WAAU,QAAQ,YAAE,OAAO,GAAE;AAAA,QACpD;AAAA,UAAC;AAAA;AAAA,YACC,IAAG;AAAA,YACH,MAAK;AAAA,YACL,cAAa;AAAA,YACZ,GAAG,SAAS,OAAO;AAAA;AAAA,QACtB;AAAA,QACA,4CAAC,uCAAgB,MAAM,OAAO,OAAO,SAAS,SAAS,GAAG;AAAA,QAE1D,4CAAC,yBAAM,SAAQ,YAAW,WAAU,aAAa,YAAE,UAAU,GAAE;AAAA,QAC/D;AAAA,UAAC;AAAA;AAAA,YACC,IAAG;AAAA,YACH,cAAa;AAAA,YACZ,GAAG,SAAS,UAAU;AAAA;AAAA,QACzB;AAAA,QACA,4CAAC,uCAAgB,MAAM,OAAO,UAAU,SAAS,SAAS,GAAG;AAAA,QAE7D,4CAAC,0BAAW,MAAM,IAAI,KAAK,gBAAgB,WAAU,gBAClD,YAAE,kBAAkB,GACvB;AAAA,QAEA,4CAAC,0BAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,SAAS,GACd;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": []}