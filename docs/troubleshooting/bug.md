---
title: Bug
description: "Follow these simple steps to report bugs and help improve our product for everyone:"
---

<img 
  src="/docs/images/bug_report.gif" 
  alt="Bug Report"
  width="100%"
  height="auto"
/>

### Step 1: Access the Bug Report Feature

1. Locate the **Bug Report** button in the top section of the desktop interface, positioned to the right of your project name
2. Click the **Bug Report** button to initiate the reporting process

### Step 2: Download Log Files

- Upon clicking the Bug Report button, log files will be automatically downloaded to your device
- These log files contain technical information that helps our development team diagnose issues more effectively

### Step 3: Complete the Bug Report Form

- A bug report web form will automatically open in your default browser
- Please provide the following information:
    - **Bug Description**: Write a clear description of the issue you encountered
    - **Screenshot Upload**: Attach relevant screenshots that demonstrate the problem
    - **Log File Upload**: Upload the log files that were downloaded in Step 2

### Step 4: Join Our Community for Real-time Support

### For English-speaking Users:

- Join our **CAMEL-AI Discord**  **🤖｜eigent** channel : https://discord.gg/HgSesTVC for bug discussions and community support
- Get direct assistance from our team and community members

### For Chinese-speaking Users:

- Add our WeChat assistant: **<PERSON><PERSON><PERSON><PERSON><PERSON>** to join the **bug fix group** for Chinese community support
- Communicate with our Chinese-speaking support team

### Alternative: GitHub Issues

Developers and technical users are welcome to report issues directly through our GitHub issues:

- **Repository URL**: https://github.com/eigent-ai/eigent
- Submit detailed issues with reproduction steps

## Important Notes

- Always include log files when reporting bugs for faster resolution
- Provide as much detail as possible in your bug description
- Screenshots help our team understand visual issues more quickly
- Our community channels provide the fastest response times for urgent issues

Thank you for helping us improve our product through your feedback and bug reports!