---
title: "Concepts"
description: "Understand the core terms and features that make <PERSON><PERSON>nt unique."
icon: "key"
---

## Workers

Autonomous agents tailored to specific roles that run tasks independently or together. Think of them as individual members of your team, like a "Researcher," a "Programmer," or a "Writer."

Each Worker is designed with specific capabilities and can be customized to handle particular types of tasks efficiently.

![Workers concept illustration](/docs/images/concepts_worker.png)

## Workforce

A coordinated team of Workers that collaborate to complete complex workflows. Think of it as your AI project team.

The Workforce orchestrates multiple Workers, ensuring they work together seamlessly to achieve your goals.

![Workforce collaboration illustration](/docs/images/concepts_workforce.gif)

## Workspace

A live window into a Worker's process where you can watch or take control. For example, a terminal, a browser, or a file viewer.

Workspaces provide real-time visibility into what your Workers are doing, allowing you to monitor progress and intervene when needed.

![Workspace interface illustration](/docs/images/concepts_workspace.gif)

## Tasks & Subtasks

You define a mission (task), the Workforce breaks it into components (subtasks), and assigns them to the appropriate Workers.

This hierarchical approach ensures complex projects are broken down into manageable pieces and executed efficiently.

![Tasks and subtasks breakdown illustration](/docs/images/concepts_tasks_subtasks.gif)

## Chat

Your primary interface for communicating with your Workforce. You use it to define your main Task, sharing files and interacting with agents in real time.

The Chat interface serves as your command center, where you can give instructions, ask questions, and receive updates from your AI team.

![Chat interface illustration](/docs/images/concepts_chat.png)

## MCP

Model Context Protocol that allows Workers to use external tools. It connects your agents to databases, APIs, and documentation sources, empowering them to act across platforms.

MCP extends your Workers' capabilities by providing access to real-world data and tools, making them more powerful and versatile.

![MCP protocol illustration](/docs/images/concepts_mcp.png)

## Models

Different AI "brains" that power your Workers. Eigent allows you to choose from various models (like GPT-4.1 or Gemini 2.5 Pro), each with different strengths in speed, reasoning, and cost.

Choose the right model for each task based on your specific needs for performance, accuracy, or cost efficiency.

![AI models illustration](/docs/images/concepts_models.png)