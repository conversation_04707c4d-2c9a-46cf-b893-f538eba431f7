"""
电商需求洞察应用 - 业务逻辑服务层
处理电商分析的核心业务逻辑
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from loguru import logger

from app.model.ecommerce import (
    EcommerceTaskData,
    EcommerceTaskStatus,
    ProcessedKeywords,
    LinkDiscoveryResult,
    CommentCollectionResult,
    AnalysisResult,
    ExportRequest,
    ExportResponse,
    LoginStatusResult,
    PlatformLoginStatus,
    Platform,
    create_task_progress,
    update_task_data
)


class EcommerceTaskService:
    """电商分析任务服务"""
    
    def __init__(self):
        self.tasks: Dict[str, EcommerceTaskData] = {}
        self.data_dir = Path.home() / ".eigent" / "ecommerce_tasks"
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    async def create_task(self, task_data: EcommerceTaskData) -> str:
        """创建新任务"""
        self.tasks[task_data.task_id] = task_data
        await self._save_task(task_data)
        return task_data.task_id
    
    async def get_task(self, task_id: str) -> Optional[EcommerceTaskData]:
        """获取任务数据"""
        if task_id in self.tasks:
            return self.tasks[task_id]
        
        # 尝试从文件加载
        task_file = self.data_dir / f"{task_id}.json"
        if task_file.exists():
            try:
                with open(task_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                task_data = EcommerceTaskData.parse_obj(data)
                self.tasks[task_id] = task_data
                return task_data
            except Exception as e:
                logger.error(f"加载任务文件失败: {e}")
        
        return None
    
    async def update_task(self, task_id: str, **kwargs) -> bool:
        """更新任务数据"""
        if task_id not in self.tasks:
            return False
        
        task_data = self.tasks[task_id]
        update_task_data(task_data, **kwargs)
        await self._save_task(task_data)
        return True
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
        
        task_file = self.data_dir / f"{task_id}.json"
        if task_file.exists():
            task_file.unlink()
        
        return True
    
    async def get_task_list(self, limit: int = 20, offset: int = 0) -> List[EcommerceTaskData]:
        """获取任务列表"""
        # 加载所有任务文件
        all_tasks = []
        for task_file in self.data_dir.glob("*.json"):
            try:
                with open(task_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                task_data = EcommerceTaskData.parse_obj(data)
                all_tasks.append(task_data)
            except Exception as e:
                logger.error(f"加载任务文件失败: {task_file}, {e}")
        
        # 按创建时间排序
        all_tasks.sort(key=lambda x: x.created_at, reverse=True)
        
        # 分页
        return all_tasks[offset:offset + limit]
    
    async def process_task(self, task_id: str):
        """处理电商分析任务的主流程"""
        try:
            task_data = await self.get_task(task_id)
            if not task_data:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始处理电商分析任务: {task_id}")
            
            # 阶段1: 关键词处理
            await self._process_keywords(task_data)
            
            # 如果需要用户确认，等待确认
            if task_data.progress.status == EcommerceTaskStatus.user_confirming:
                logger.info(f"等待用户确认关键词: {task_id}")
                return
            
            # 阶段2: 链接发现
            await self._discover_links(task_data)
            
            # 阶段3: 评论采集
            await self._collect_comments(task_data)
            
            # 阶段4: AI分析
            await self._analyze_comments(task_data)
            
            # 完成任务
            await self._complete_task(task_data)
            
        except Exception as e:
            logger.error(f"处理任务失败: {task_id}, {e}")
            await self._fail_task(task_id, str(e))
    
    async def confirm_keywords(self, task_id: str, confirmed_keywords: List[str]):
        """确认关键词并继续处理"""
        task_data = await self.get_task(task_id)
        if not task_data or not task_data.processed_keywords:
            return
        
        # 更新最终关键词
        task_data.processed_keywords.final = confirmed_keywords
        task_data.progress = create_task_progress(
            EcommerceTaskStatus.link_discovering,
            "开始发现商品链接",
            30
        )
        
        await self.update_task(task_id, 
                             processed_keywords=task_data.processed_keywords,
                             progress=task_data.progress)
        
        # 继续处理任务
        asyncio.create_task(self._continue_after_confirmation(task_id))
    
    async def pause_task(self, task_id: str):
        """暂停任务"""
        await self.update_task(task_id, 
                             progress=create_task_progress(EcommerceTaskStatus.paused, "任务已暂停"))
    
    async def resume_task(self, task_id: str):
        """恢复任务"""
        task_data = await self.get_task(task_id)
        if task_data:
            # 根据当前状态恢复到适当的阶段
            if task_data.processed_keywords and not task_data.link_discovery:
                status = EcommerceTaskStatus.link_discovering
                step = "恢复链接发现"
            elif task_data.link_discovery and not task_data.comment_collection:
                status = EcommerceTaskStatus.comment_collecting
                step = "恢复评论采集"
            elif task_data.comment_collection and not task_data.analysis:
                status = EcommerceTaskStatus.ai_analyzing
                step = "恢复AI分析"
            else:
                status = EcommerceTaskStatus.pending
                step = "恢复任务处理"
            
            await self.update_task(task_id, 
                                 progress=create_task_progress(status, step))
            
            # 重新启动处理
            asyncio.create_task(self.process_task(task_id))
    
    async def _save_task(self, task_data: EcommerceTaskData):
        """保存任务到文件"""
        task_file = self.data_dir / f"{task_data.task_id}.json"
        try:
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_data.dict(), f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存任务文件失败: {e}")
    
    async def _process_keywords(self, task_data: EcommerceTaskData):
        """处理关键词阶段"""
        logger.info(f"开始处理关键词: {task_data.task_id}")

        # 更新状态
        task_data.progress = create_task_progress(
            EcommerceTaskStatus.keyword_processing,
            "正在处理关键词",
            10
        )
        await self._save_task(task_data)

        # 集成KeywordProcessorAgent
        try:
            from app.utils.ecommerce_agents import keyword_processor_agent
            from app.model.chat import Chat

            # 创建Chat选项（模拟）
            chat_options = Chat(
                task_id=task_data.task_id,
                question="处理关键词",
                email="<EMAIL>",
                model_platform="openai",
                model_type="gpt-4",
                api_key="dummy_key"  # TODO: 使用实际配置
            )

            # 创建关键词处理智能体
            agent = await keyword_processor_agent(chat_options)

            # 解析输入的关键词
            keywords = self._parse_keywords(task_data.keyword_input.content)

            # 构建输入消息
            input_message = f"""
            请处理以下关键词：{', '.join(keywords)}
            扫描模式：{task_data.scan_mode.value}
            关键词数量：{len(keywords)}

            请根据关键词数量选择合适的处理策略：
            - 少于5个：进行关键词扩展
            - 5个以上：进行关键词聚类
            """

            # 调用智能体处理
            response = await agent.astep(input_message)

            # 解析智能体响应
            # TODO: 解析智能体返回的结构化数据
            if len(keywords) < 5 and task_data.scan_mode.value == 'deep':
                # 扩展模式
                expanded = await self._expand_keywords(keywords)
                processed_keywords = ProcessedKeywords(
                    original=keywords,
                    expanded=expanded,
                    final=expanded
                )
            elif len(keywords) >= 5 and task_data.scan_mode.value == 'deep':
                # 聚类模式
                clustered = await self._cluster_keywords(keywords)
                final_keywords = []
                for cluster in clustered:
                    final_keywords.extend(cluster.keywords[:3])

                processed_keywords = ProcessedKeywords(
                    original=keywords,
                    clustered=clustered,
                    final=final_keywords
                )

                # 需要用户确认
                task_data.progress = create_task_progress(
                    EcommerceTaskStatus.user_confirming,
                    "等待用户确认关键词",
                    20
                )
                task_data.processed_keywords = processed_keywords
                await self._save_task(task_data)
                return
            else:
                # 快速模式
                processed_keywords = ProcessedKeywords(
                    original=keywords,
                    final=keywords
                )

            task_data.processed_keywords = processed_keywords
            task_data.progress = create_task_progress(
                EcommerceTaskStatus.link_discovering,
                "开始发现商品链接",
                30
            )
            await self._save_task(task_data)

        except Exception as e:
            logger.error(f"关键词处理失败: {e}")
            # 降级到简单处理
            keywords = self._parse_keywords(task_data.keyword_input.content)
            processed_keywords = ProcessedKeywords(
                original=keywords,
                final=keywords
            )
            task_data.processed_keywords = processed_keywords
            task_data.progress = create_task_progress(
                EcommerceTaskStatus.link_discovering,
                "开始发现商品链接",
                30
            )
            await self._save_task(task_data)
    
    async def _continue_after_confirmation(self, task_id: str):
        """用户确认后继续处理"""
        task_data = await self.get_task(task_id)
        if not task_data:
            return
        
        # 继续后续阶段
        await self._discover_links(task_data)
        await self._collect_comments(task_data)
        await self._analyze_comments(task_data)
        await self._complete_task(task_data)
    
    async def _discover_links(self, task_data: EcommerceTaskData):
        """链接发现阶段"""
        logger.info(f"开始发现链接: {task_data.task_id}")

        # 集成EcommerceCrawlerAgent的链接发现功能
        try:
            from app.utils.ecommerce_agents import ecommerce_crawler_agent
            from app.model.chat import Chat

            # 创建Chat选项
            chat_options = Chat(
                task_id=task_data.task_id,
                question="发现商品链接",
                email="<EMAIL>",
                model_platform="openai",
                model_type="gpt-4",
                api_key="dummy_key"
            )

            # 创建爬虫智能体
            agent = await ecommerce_crawler_agent(chat_options)

            # 获取最终关键词
            keywords = task_data.processed_keywords.final if task_data.processed_keywords else []

            # 构建输入消息
            input_message = f"""
            请执行链接发现任务：
            关键词列表：{', '.join(keywords)}

            请严格按照三个阶段执行：
            1. 阶段一：链接发现 - 对每个关键词发现淘宝和小红书链接
            2. 阶段二：去重筛选 - 去重并按销量/点赞数排序
            3. 阶段三：评论采集 - 轮询式采集评论数据

            请开始执行阶段一：链接发现
            """

            # 调用智能体处理
            response = await agent.astep(input_message)

            # TODO: 解析智能体返回的链接数据
            # 目前使用模拟数据
            link_discovery = LinkDiscoveryResult(
                taobao=[],
                xiaohongshu=[],
                total=0,
                duplicates_removed=0
            )

            task_data.link_discovery = link_discovery
            task_data.progress = create_task_progress(
                EcommerceTaskStatus.comment_collecting,
                "开始采集评论",
                60
            )
            await self._save_task(task_data)

        except Exception as e:
            logger.error(f"链接发现失败: {e}")
            # 降级处理
            link_discovery = LinkDiscoveryResult(
                taobao=[],
                xiaohongshu=[],
                total=0,
                duplicates_removed=0
            )
            task_data.link_discovery = link_discovery
            task_data.progress = create_task_progress(
                EcommerceTaskStatus.comment_collecting,
                "开始采集评论",
                60
            )
            await self._save_task(task_data)
    
    async def _collect_comments(self, task_data: EcommerceTaskData):
        """评论采集阶段"""
        logger.info(f"开始采集评论: {task_data.task_id}")

        # 评论采集由EcommerceCrawlerAgent在链接发现阶段一并完成
        # 这里主要是状态更新和数据整理
        try:
            # TODO: 从智能体获取评论采集结果
            # 目前使用模拟数据
            comment_collection = CommentCollectionResult(
                links=[],
                total_comments=0,
                successful_links=0,
                failed_links=0
            )

            task_data.comment_collection = comment_collection
            task_data.progress = create_task_progress(
                EcommerceTaskStatus.ai_analyzing,
                "开始AI分析",
                80
            )
            await self._save_task(task_data)

        except Exception as e:
            logger.error(f"评论采集失败: {e}")
            # 降级处理
            comment_collection = CommentCollectionResult(
                links=[],
                total_comments=0,
                successful_links=0,
                failed_links=0
            )
            task_data.comment_collection = comment_collection
            task_data.progress = create_task_progress(
                EcommerceTaskStatus.ai_analyzing,
                "开始AI分析",
                80
            )
            await self._save_task(task_data)
    
    async def _analyze_comments(self, task_data: EcommerceTaskData):
        """AI分析阶段"""
        logger.info(f"开始AI分析: {task_data.task_id}")

        # 集成CommentAnalyzerAgent
        try:
            from app.utils.ecommerce_agents import comment_analyzer_agent
            from app.model.chat import Chat

            # 创建Chat选项
            chat_options = Chat(
                task_id=task_data.task_id,
                question="分析评论数据",
                email="<EMAIL>",
                model_platform="openai",
                model_type="gpt-4",
                api_key="dummy_key"
            )

            # 创建评论分析智能体
            agent = await comment_analyzer_agent(chat_options)

            # 获取评论数据
            comment_data = task_data.comment_collection
            if not comment_data or not comment_data.links:
                logger.warning("没有评论数据可供分析")
                analysis = AnalysisResult(
                    map_results=[],
                    opportunities=[],
                    summary="没有评论数据可供分析",
                    analyzed_at=datetime.now()
                )
            else:
                # 构建输入消息
                input_message = f"""
                请分析以下评论数据：

                总评论数：{comment_data.total_comments}
                成功采集链接数：{comment_data.successful_links}

                请执行Map-Reduce分析：
                1. Map阶段：分析每个产品的评论，提取优缺点和机会点
                2. Reduce阶段：汇总所有分析结果，识别市场机会洞察

                请开始分析...
                """

                # 调用智能体处理
                response = await agent.astep(input_message)

                # TODO: 解析智能体返回的分析结果
                # 目前使用模拟数据
                analysis = AnalysisResult(
                    map_results=[],
                    opportunities=[],
                    summary="AI分析完成",
                    analyzed_at=datetime.now()
                )

            task_data.analysis = analysis
            await self._save_task(task_data)

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            # 降级处理
            analysis = AnalysisResult(
                map_results=[],
                opportunities=[],
                summary=f"分析失败: {str(e)}",
                analyzed_at=datetime.now()
            )
            task_data.analysis = analysis
            await self._save_task(task_data)
    
    async def _complete_task(self, task_data: EcommerceTaskData):
        """完成任务"""
        logger.info(f"任务完成: {task_data.task_id}")
        
        task_data.progress = create_task_progress(
            EcommerceTaskStatus.completed,
            "任务已完成",
            100
        )
        task_data.completed_at = datetime.now()
        await self._save_task(task_data)
    
    async def _fail_task(self, task_id: str, error: str):
        """任务失败"""
        await self.update_task(task_id,
                             progress=create_task_progress(EcommerceTaskStatus.failed, f"任务失败: {error}"),
                             error=error)
    
    def _parse_keywords(self, content: str) -> List[str]:
        """解析关键词字符串"""
        # 支持多种分隔符
        import re
        keywords = re.split(r'[、，,\s\n;；]+', content.strip())
        return [kw.strip() for kw in keywords if kw.strip()]
    
    async def _expand_keywords(self, keywords: List[str]) -> List[str]:
        """扩展关键词（少于5个时）"""
        # TODO: 调用淘宝API获取下拉词，然后AI扩展
        expanded = keywords.copy()
        for kw in keywords:
            expanded.extend([f"{kw}推荐", f"{kw}热销", f"{kw}优质"])
        return expanded[:15]  # 最多15个
    
    async def _cluster_keywords(self, keywords: List[str]):
        """聚类关键词（5个以上时）"""
        # TODO: AI聚类逻辑
        from app.model.ecommerce import KeywordCluster
        
        # 简单模拟聚类
        clusters = []
        chunk_size = max(3, len(keywords) // 3)
        for i in range(0, len(keywords), chunk_size):
            chunk = keywords[i:i + chunk_size]
            clusters.append(KeywordCluster(
                theme=f"主题{len(clusters) + 1}",
                keywords=chunk
            ))
        
        return clusters


class PlatformLoginService:
    """平台登录服务"""

    def __init__(self):
        self.login_data_dir = Path.home() / ".eigent" / "login_data"
        self.login_data_dir.mkdir(parents=True, exist_ok=True)

    async def check_all_platforms(self) -> LoginStatusResult:
        """检查所有平台登录状态"""
        taobao_status = await self._check_platform_login(Platform.taobao)
        xiaohongshu_status = await self._check_platform_login(Platform.xiaohongshu)

        return LoginStatusResult(
            taobao=taobao_status,
            xiaohongshu=xiaohongshu_status
        )

    async def _check_platform_login(self, platform: Platform) -> PlatformLoginStatus:
        """检查单个平台登录状态"""
        try:
            # 检查登录文件是否存在
            login_file = self.login_data_dir / f"{platform.value}_session.json"
            file_exists = login_file.exists()

            if not file_exists:
                return PlatformLoginStatus(
                    platform=platform,
                    is_logged_in=False,
                    session_valid=False,
                    file_exists=False,
                    error="登录文件不存在"
                )

            # 读取登录数据
            try:
                with open(login_file, 'r', encoding='utf-8') as f:
                    login_data = json.load(f)

                # 检查会话是否过期
                last_login_time = datetime.fromisoformat(login_data.get('last_login_time', ''))
                session_valid = (datetime.now() - last_login_time).days < 7  # 7天内有效

                return PlatformLoginStatus(
                    platform=platform,
                    is_logged_in=True,
                    session_valid=session_valid,
                    file_exists=True,
                    last_login_time=last_login_time
                )

            except (json.JSONDecodeError, ValueError, KeyError) as e:
                return PlatformLoginStatus(
                    platform=platform,
                    is_logged_in=False,
                    session_valid=False,
                    file_exists=True,
                    error=f"登录文件格式错误: {str(e)}"
                )

        except Exception as e:
            logger.error(f"检查{platform.value}登录状态失败: {e}")
            return PlatformLoginStatus(
                platform=platform,
                is_logged_in=False,
                session_valid=False,
                file_exists=False,
                error=str(e)
            )

    async def start_login(self, platform: str) -> str:
        """启动平台登录"""
        try:
            # 创建登录脚本
            login_script = await self._create_login_script(platform)

            # 启动浏览器登录流程
            login_url = await self._launch_browser_login(platform, login_script)

            return login_url

        except Exception as e:
            logger.error(f"启动{platform}登录失败: {e}")
            raise

    async def _create_login_script(self, platform: str) -> str:
        """创建登录脚本"""
        if platform == "taobao":
            return """
            // 淘宝登录脚本
            const loginUrl = 'https://login.taobao.com/member/login.jhtml';
            window.open(loginUrl, '_blank');

            // 监听登录成功
            const checkLogin = () => {
                if (document.cookie.includes('_tb_token_')) {
                    // 保存登录状态
                    const sessionData = {
                        platform: 'taobao',
                        cookies: document.cookie,
                        last_login_time: new Date().toISOString(),
                        user_agent: navigator.userAgent
                    };

                    // 通知后端保存登录状态
                    fetch('/api/ecommerce/login/save', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(sessionData)
                    });

                    alert('淘宝登录成功！');
                    window.close();
                } else {
                    setTimeout(checkLogin, 2000);
                }
            };

            setTimeout(checkLogin, 5000);
            """
        elif platform == "xiaohongshu":
            return """
            // 小红书登录脚本
            const loginUrl = 'https://www.xiaohongshu.com/explore';
            window.open(loginUrl, '_blank');

            // 监听登录成功
            const checkLogin = () => {
                if (document.cookie.includes('web_session')) {
                    // 保存登录状态
                    const sessionData = {
                        platform: 'xiaohongshu',
                        cookies: document.cookie,
                        last_login_time: new Date().toISOString(),
                        user_agent: navigator.userAgent
                    };

                    // 通知后端保存登录状态
                    fetch('/api/ecommerce/login/save', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(sessionData)
                    });

                    alert('小红书登录成功！');
                    window.close();
                } else {
                    setTimeout(checkLogin, 2000);
                }
            };

            setTimeout(checkLogin, 5000);
            """
        else:
            raise ValueError(f"不支持的平台: {platform}")

    async def _launch_browser_login(self, platform: str, script: str) -> str:
        """启动浏览器登录"""
        # 创建临时HTML文件
        temp_file = self.login_data_dir / f"{platform}_login.html"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{platform.title()}登录</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>正在启动{platform.title()}登录...</h1>
            <p>请在新打开的窗口中完成登录</p>
            <script>
                {script}
            </script>
        </body>
        </html>
        """

        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return f"file://{temp_file.absolute()}"

    async def save_login_session(self, session_data: Dict[str, Any]) -> bool:
        """保存登录会话数据"""
        try:
            platform = session_data.get('platform')
            if not platform:
                raise ValueError("缺少平台信息")

            login_file = self.login_data_dir / f"{platform}_session.json"

            with open(login_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

            logger.info(f"{platform}登录会话已保存")
            return True

        except Exception as e:
            logger.error(f"保存登录会话失败: {e}")
            return False


class DataExportService:
    """数据导出服务"""
    
    async def export_data(self, task_data: EcommerceTaskData, request: ExportRequest) -> ExportResponse:
        """导出数据"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = request.filename or f"电商分析_{request.data_type}_{timestamp}.{request.format}"
            
            # 创建导出目录
            export_dir = Path.home() / ".eigent" / "exports"
            export_dir.mkdir(parents=True, exist_ok=True)
            file_path = export_dir / filename
            
            # 根据格式和数据类型导出
            content = await self._generate_export_content(task_data, request)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            file_size = file_path.stat().st_size
            
            return ExportResponse(
                success=True,
                filename=filename,
                file_path=str(file_path),
                size=file_size
            )
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return ExportResponse(
                success=False,
                filename="",
                file_path="",
                size=0,
                error=str(e)
            )
    
    async def _generate_export_content(self, task_data: EcommerceTaskData, request: ExportRequest) -> str:
        """生成导出内容"""
        # TODO: 根据不同的格式和数据类型生成相应的内容
        if request.format == "markdown":
            return self._generate_markdown_content(task_data, request.data_type)
        elif request.format == "csv":
            return self._generate_csv_content(task_data, request.data_type)
        elif request.format == "excel":
            return self._generate_excel_content(task_data, request.data_type)
        else:
            raise ValueError(f"不支持的导出格式: {request.format}")
    
    def _generate_markdown_content(self, task_data: EcommerceTaskData, data_type: str) -> str:
        """生成Markdown内容"""
        content = f"# 电商需求洞察分析报告\n\n"
        content += f"**任务ID**: {task_data.task_id}\n"
        content += f"**扫描模式**: {task_data.scan_mode}\n"
        content += f"**创建时间**: {task_data.created_at}\n\n"
        
        if data_type == "report" and task_data.analysis:
            content += "## 市场机会洞察\n\n"
            for i, opportunity in enumerate(task_data.analysis.opportunities, 1):
                content += f"### 机会{i}: {opportunity.title}\n\n"
                content += f"{opportunity.description}\n\n"
                content += "**支撑证据**:\n"
                for evidence in opportunity.evidence:
                    content += f"- {evidence}\n"
                content += "\n"
        
        return content
    
    def _generate_csv_content(self, task_data: EcommerceTaskData, data_type: str) -> str:
        """生成CSV内容"""
        # TODO: 实现CSV格式导出
        return "CSV内容待实现"
    
    def _generate_excel_content(self, task_data: EcommerceTaskData, data_type: str) -> str:
        """生成Excel内容"""
        # TODO: 实现Excel格式导出
        return "Excel内容待实现"
