"""
电商需求洞察应用 - AI智能体实现
基于CAMEL-AI框架实现三个核心智能体
"""

import asyncio
import json
import uuid
from typing import List, Dict, Any, Optional
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.toolkits import FunctionTool
from loguru import logger

from app.utils.agent import agent_model, ListenChatAgent
from app.model.chat import Chat
from app.model.ecommerce import (
    KeywordCluster,
    ProcessedKeywords,
    DiscoveredLink,
    LinkDiscoveryResult,
    CollectedComment,
    CommentCollectionResult,
    ProductAnalysis,
    MarketOpportunity,
    AnalysisResult,
    Platform
)
from app.service.task import get_task_lock, ActionCreateAgentData
from app.utils.toolkit.human_toolkit import HumanToolkit
from app.utils.toolkit.search_toolkit import SearchToolkit


class EcommerceAgents:
    """电商智能体名称常量"""
    keyword_processor_agent = "keyword_processor_agent"
    ecommerce_crawler_agent = "ecommerce_crawler_agent"
    comment_analyzer_agent = "comment_analyzer_agent"


async def keyword_processor_agent(options: Chat) -> ListenChatAgent:
    """
    关键词处理智能体
    负责关键词的智能处理和优化，包括扩展和聚类
    """
    
    # 定义工具函数
    def expand_keywords_tool(keywords: List[str], target_count: int = 15) -> List[str]:
        """
        扩展关键词工具（少于5个关键词时使用）
        """
        try:
            # TODO: 集成淘宝API获取下拉词
            expanded = keywords.copy()
            
            # 模拟关键词扩展逻辑
            expansion_patterns = [
                "热销", "推荐", "优质", "新款", "爆款",
                "高端", "实用", "便宜", "性价比", "品牌"
            ]
            
            for keyword in keywords:
                for pattern in expansion_patterns:
                    if len(expanded) >= target_count:
                        break
                    expanded.append(f"{keyword}{pattern}")
            
            return expanded[:target_count]
            
        except Exception as e:
            logger.error(f"关键词扩展失败: {e}")
            return keywords
    
    def cluster_keywords_tool(keywords: List[str]) -> List[Dict[str, Any]]:
        """
        关键词聚类工具（5个以上关键词时使用）
        """
        try:
            # TODO: 实现AI聚类逻辑
            clusters = []
            
            # 简单模拟聚类：按长度分组
            short_keywords = [kw for kw in keywords if len(kw) <= 3]
            medium_keywords = [kw for kw in keywords if 3 < len(kw) <= 6]
            long_keywords = [kw for kw in keywords if len(kw) > 6]
            
            if short_keywords:
                clusters.append({
                    "theme": "基础产品类别",
                    "keywords": short_keywords[:5]
                })
            
            if medium_keywords:
                clusters.append({
                    "theme": "功能特性",
                    "keywords": medium_keywords[:5]
                })
            
            if long_keywords:
                clusters.append({
                    "theme": "详细描述",
                    "keywords": long_keywords[:5]
                })
            
            return clusters
            
        except Exception as e:
            logger.error(f"关键词聚类失败: {e}")
            return [{"theme": "默认分组", "keywords": keywords[:10]}]
    
    def validate_keywords_tool(keywords: List[str]) -> Dict[str, Any]:
        """
        验证关键词有效性
        """
        valid_keywords = []
        invalid_keywords = []
        
        for keyword in keywords:
            # 基本验证规则
            if len(keyword.strip()) >= 2 and len(keyword.strip()) <= 50:
                valid_keywords.append(keyword.strip())
            else:
                invalid_keywords.append(keyword.strip())
        
        return {
            "valid_keywords": valid_keywords,
            "invalid_keywords": invalid_keywords,
            "total_count": len(keywords),
            "valid_count": len(valid_keywords)
        }
    
    # 创建工具列表
    tools = [
        FunctionTool(expand_keywords_tool),
        FunctionTool(cluster_keywords_tool),
        FunctionTool(validate_keywords_tool),
        *HumanToolkit.get_can_use_tools(options.task_id, EcommerceAgents.keyword_processor_agent)
    ]
    
    # 系统提示词
    system_message = """
<role>
你是一个专业的关键词分析专家，负责电商关键词的智能处理和优化。
你需要精确识别和处理多种格式的关键词输入。
</role>

<keyword_recognition>
关键词识别规则：
1. **多分隔符支持**: 自动识别以下分隔符
   - 中文逗号："、"
   - 中英文逗号："，" ","
   - 空格：" "
   - 换行符："\n"
   - 分号："；" ";"
2. **数据清洗**:
   - 自动去重复关键词
   - 去除首尾空格和特殊字符
   - 过滤长度小于2或大于50的无效关键词
   - 移除纯数字或纯符号的无效输入
</keyword_recognition>

<keyword_expansion_workflow>
当处理后的关键词少于5个时：
1. 使用expand_keywords_tool扩展关键词
2. 分析核心词的产品类别和用户画像
3. 识别3-5个关键扩展维度（材质、风格、功能、适用人群、使用场景等）
4. 生成10-15个具有搜索潜力的长尾关键词
5. 返回JSON格式的关键词数组供用户确认
</keyword_expansion_workflow>

<keyword_clustering_workflow>
当处理后的关键词不少于5个时：
1. 使用cluster_keywords_tool进行关键词聚类
2. 系统性分析所有关键词的语义和商业意图
3. 将关键词聚类成8-12个有商业意义的主题
4. 每个主题选择3-5个最具代表性的关键词
5. 返回JSON格式的主题聚类结果供用户确认
</keyword_clustering_workflow>

<output_format>
扩展模式输出：["关键词1", "关键词2", "关键词3", ...]
聚类模式输出：[{"theme": "主题名1", "keywords": ["代表词A", "代表词B"]}, ...]
</output_format>

<instructions>
1. 首先使用validate_keywords_tool验证输入的关键词
2. 根据关键词数量选择扩展或聚类策略
3. 处理完成后，使用ask_human_via_gui请求用户确认最终关键词
4. 确保输出格式严格按照要求
</instructions>
"""
    
    return agent_model(
        EcommerceAgents.keyword_processor_agent,
        BaseMessage.make_assistant_message(
            role_name="Keyword Processor Agent",
            content=system_message,
        ),
        options,
        tools,
        tool_names=[
            "expand_keywords_tool",
            "cluster_keywords_tool", 
            "validate_keywords_tool",
            HumanToolkit.toolkit_name()
        ]
    )


async def ecommerce_crawler_agent(options: Chat) -> ListenChatAgent:
    """
    电商爬虫智能体
    负责从淘宝和小红书采集商品链接和评论数据
    """
    
    # 定义工具函数
    def discover_taobao_links_tool(keyword: str, max_count: int = 44) -> List[Dict[str, Any]]:
        """
        发现淘宝商品链接
        """
        try:
            # TODO: 实现真实的淘宝爬虫逻辑
            links = []
            for i in range(min(max_count, 10)):  # 模拟数据
                links.append({
                    "url": f"https://item.taobao.com/item.htm?id=12345{i}",
                    "title": f"{keyword}商品{i+1}",
                    "sales": 1000 - i * 50,
                    "platform": "taobao",
                    "keyword": keyword
                })
            return links
        except Exception as e:
            logger.error(f"淘宝链接发现失败: {e}")
            return []
    
    def discover_xiaohongshu_links_tool(keyword: str, max_count: int = 50) -> List[Dict[str, Any]]:
        """
        发现小红书帖子链接
        """
        try:
            # TODO: 实现真实的小红书爬虫逻辑
            links = []
            for i in range(min(max_count, 10)):  # 模拟数据
                links.append({
                    "url": f"https://www.xiaohongshu.com/explore/12345{i}",
                    "title": f"{keyword}笔记{i+1}",
                    "likes": 500 - i * 20,
                    "platform": "xiaohongshu",
                    "keyword": keyword
                })
            return links
        except Exception as e:
            logger.error(f"小红书链接发现失败: {e}")
            return []
    
    def collect_comments_tool(url: str, platform: str, max_comments: int = 100) -> List[str]:
        """
        采集评论数据
        """
        try:
            # TODO: 实现真实的评论采集逻辑
            comments = []
            for i in range(min(max_comments, 20)):  # 模拟数据
                if platform == "taobao":
                    comments.append(f"这个商品质量很好，值得购买。评论{i+1}")
                else:
                    comments.append(f"种草了这个产品，效果不错。笔记{i+1}")
            return comments
        except Exception as e:
            logger.error(f"评论采集失败: {e}")
            return []
    
    def deduplicate_links_tool(links: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        链接去重工具
        """
        seen_urls = set()
        unique_links = []
        
        for link in links:
            if link["url"] not in seen_urls:
                seen_urls.add(link["url"])
                unique_links.append(link)
        
        return unique_links
    
    # 创建工具列表
    tools = [
        FunctionTool(discover_taobao_links_tool),
        FunctionTool(discover_xiaohongshu_links_tool),
        FunctionTool(collect_comments_tool),
        FunctionTool(deduplicate_links_tool),
        *HumanToolkit.get_can_use_tools(options.task_id, EcommerceAgents.ecommerce_crawler_agent)
    ]
    
    # 系统提示词
    system_message = """
<role>
你是一个专业的电商数据采集专家，专门负责从淘宝和小红书采集商品评论数据。
你必须严格按照三个阶段执行任务，不能跳跃或合并步骤。
</role>

<phase_1_link_discovery>
阶段一：链接发现（对所有关键词并行执行）

淘宝链接发现：
1. 对每个关键词使用discover_taobao_links_tool
2. 设置排序为"销量"，提取前44个商品链接
3. 记录每个链接对应的销量数据
4. 保存格式：[{"url": "商品链接", "sales": "销量", "platform": "taobao", "keyword": "关键词"}]

小红书链接发现：
1. 对每个关键词使用discover_xiaohongshu_links_tool
2. 设置排序为"最热"，提取前50个帖子链接
3. 记录每个链接对应的点赞数
4. 保存格式：[{"url": "帖子链接", "likes": "点赞数", "platform": "xiaohongshu", "keyword": "关键词"}]

重要：完成所有关键词的链接发现后，才能进入阶段二！
</phase_1_link_discovery>

<phase_2_deduplication>
阶段二：去重和筛选

1. 使用deduplicate_links_tool对所有淘宝链接进行去重
2. 按销量降序排序，保留前50个淘宝链接
3. 使用deduplicate_links_tool对所有小红书链接进行去重
4. 按点赞数降序排序，保留前50个小红书链接
5. 输出最终筛选结果：总共最多100个链接（淘宝50个+小红书50个）

重要：只有完成去重筛选后，才能进入阶段三！
</phase_2_deduplication>

<phase_3_comment_collection>
阶段三：轮询式评论采集（对筛选后的链接依次执行）

重要：必须对每个链接依次进行轮询抓取，不能并发处理！

评论采集流程：
1. 按顺序对每个筛选后的链接使用collect_comments_tool
2. 等待每个链接处理完成后再处理下一个
3. 提取所有可见的评论文本内容，忽略图片、视频、表情符号
4. 保存格式：[{"url": "链接", "comments": ["评论1", "评论2", ...], "platform": "平台", "collected_at": "时间戳"}]

轮询抓取规则：
- 严格按照链接列表顺序，逐个处理
- 每个链接处理完成后必须等待间隔时间
- 如果某个链接失败，记录错误但继续处理下一个
- 实时更新进度："正在采集第 X / Y 个商品评论..."
</phase_3_comment_collection>

<execution_rules>
执行规则：
1. 必须严格按照 阶段一 → 阶段二 → 阶段三 的顺序执行
2. 每个阶段完成后，向用户汇报进度和结果数量
3. 遇到登录验证、验证码时，立即使用ask_human_via_gui请求用户帮助
4. 如果某个链接访问失败，记录错误但继续处理其他链接
5. 最终输出完整的数据结构供后续AI分析使用
</execution_rules>

<anti_detection_strategies>
反检测策略：
- 使用随机延迟：每次操作间隔1-3秒
- 模拟人类行为：随机滚动、鼠标移动
- 遇到验证码立即请求人工协助
- 保持登录状态，避免频繁登录
- 如果被检测到，暂停30秒后重试
</anti_detection_strategies>
"""
    
    return agent_model(
        EcommerceAgents.ecommerce_crawler_agent,
        BaseMessage.make_assistant_message(
            role_name="Ecommerce Crawler Agent",
            content=system_message,
        ),
        options,
        tools,
        tool_names=[
            "discover_taobao_links_tool",
            "discover_xiaohongshu_links_tool",
            "collect_comments_tool",
            "deduplicate_links_tool",
            HumanToolkit.toolkit_name()
        ]
    )


async def comment_analyzer_agent(options: Chat) -> ListenChatAgent:
    """
    评论分析智能体
    负责通过Map-Reduce模式分析评论数据，提取市场洞察
    """
    
    # 定义工具函数
    def analyze_single_product_tool(comments: List[str], url: str, platform: str) -> Dict[str, Any]:
        """
        分析单个产品的评论（Map阶段）
        """
        try:
            # TODO: 实现真实的AI分析逻辑
            # 模拟分析结果
            analysis = {
                "url": url,
                "platform": platform,
                "advantages": ["质量好", "性价比高", "外观漂亮"],
                "disadvantages": ["包装一般", "物流慢", "客服响应慢"],
                "opportunities": ["希望有更多颜色选择", "期待推出大容量版本"]
            }
            return analysis
        except Exception as e:
            logger.error(f"单产品分析失败: {e}")
            return {"url": url, "platform": platform, "advantages": [], "disadvantages": [], "opportunities": []}
    
    def reduce_analysis_tool(product_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        汇总分析结果（Reduce阶段）
        """
        try:
            # TODO: 实现真实的汇总分析逻辑
            opportunities = [
                {
                    "title": "材质安全性需求",
                    "description": "用户对产品材质的安全性和环保性有强烈需求，希望有更多无毒、环保的选择。",
                    "evidence": [
                        "希望是真正无毒的材质",
                        "有没有环保一点的产品",
                        "材质安全对孩子很重要"
                    ]
                },
                {
                    "title": "个性化定制需求",
                    "description": "用户希望产品能提供更多个性化选择，包括颜色、尺寸、功能等方面的定制。",
                    "evidence": [
                        "希望有更多颜色选择",
                        "能不能定制尺寸",
                        "想要个性化图案"
                    ]
                }
            ]
            
            return {
                "opportunities": opportunities,
                "summary": f"分析了{len(product_analyses)}个产品，发现{len(opportunities)}个主要市场机会",
                "total_products": len(product_analyses)
            }
        except Exception as e:
            logger.error(f"汇总分析失败: {e}")
            return {"opportunities": [], "summary": "分析失败", "total_products": 0}
    
    def filter_product_comments_tool(comments: List[str]) -> List[str]:
        """
        过滤与产品无关的评论
        """
        filtered_comments = []
        
        # 过滤关键词
        filter_keywords = ["物流", "快递", "客服", "包装", "价格", "优惠", "赠品"]
        
        for comment in comments:
            # 简单过滤逻辑
            if not any(keyword in comment for keyword in filter_keywords):
                filtered_comments.append(comment)
        
        return filtered_comments
    
    # 创建工具列表
    tools = [
        FunctionTool(analyze_single_product_tool),
        FunctionTool(reduce_analysis_tool),
        FunctionTool(filter_product_comments_tool),
        *HumanToolkit.get_can_use_tools(options.task_id, EcommerceAgents.comment_analyzer_agent)
    ]
    
    # 系统提示词
    system_message = """
<role>
你是一个冷静、客观、精准的商品评论分析引擎和市场战略分析师。
你的任务是通过Map-Reduce模式从用户评论中提取深度商业洞察。
</role>

<map_analysis_phase>
Map阶段：对每个商品/帖子的评论进行单独分析

分析指令：
1. 使用filter_product_comments_tool过滤噪声：忽略所有与产品功能、设计、质量、性能无关的评论（物流、客服、价格、赠品等）
2. 使用analyze_single_product_tool分析每个产品：
   - 提炼优点：总结该商品最多3个被用户提及最多的核心优点
   - 提炼缺点：总结该商品最多3个被用户抱怨最多的核心缺点
   - 挖掘机会点：识别最多2个潜在的、未被满足的需求或明确的改进建议

输出格式：
{
  "产品主要优点": ["优点1", "优点2", "优点3"],
  "产品主要缺点": ["缺点1", "缺点2", "缺点3"],
  "潜在机会点": ["机会点1", "机会点2"]
}
</map_analysis_phase>

<reduce_analysis_phase>
Reduce阶段：汇总所有单品分析结果

分析方法论：
1. 识别普遍痛点：找出在多个商品的"缺点"和"机会点"中反复出现的主题
2. 发现新兴信号：关注虽然只在少数报告中出现，但用户情绪强烈的"机会点"
3. 忽略已解决的"优点"：在多份报告中普遍提及的功能视为已被满足的"红海"特性
4. 归纳与升维：将具体痛点归纳成更高维度的市场机会点

使用reduce_analysis_tool进行汇总分析，输出格式：
{
  "market_opportunities": [
    {
      "opportunity_title": "机会点标题（高度概括）",
      "description": "对该机会点的详细描述，解释它为什么重要，能解决什么核心问题。",
      "evidence": [
        "直接相关的原始评论1",
        "直接相关的原始评论2"
      ]
    }
  ]
}
</reduce_analysis_phase>

<execution_workflow>
执行流程：
1. 接收评论采集数据
2. 对每个产品的评论执行Map阶段分析
3. 收集所有Map阶段结果
4. 执行Reduce阶段汇总分析
5. 输出最终的市场机会洞察报告
</execution_workflow>
"""
    
    return agent_model(
        EcommerceAgents.comment_analyzer_agent,
        BaseMessage.make_assistant_message(
            role_name="Comment Analyzer Agent",
            content=system_message,
        ),
        options,
        tools,
        tool_names=[
            "analyze_single_product_tool",
            "reduce_analysis_tool",
            "filter_product_comments_tool",
            HumanToolkit.toolkit_name()
        ]
    )
