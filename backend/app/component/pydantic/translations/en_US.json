{"Object has no attribute '{}'": "Object has no attribute '{}'", "Invalid JSON: {}": "Invalid JSON: {}", "JSON input should be string, bytes or bytearray": "JSON input should be string, bytes or bytearray", "Cannot check `{}` when validating from json, use a JsonOrPython validator instead": "Cannot check `{}` when validating from json, use a JsonOrPython validator instead", "Recursion error - cyclic reference detected": "Recursion error - cyclic reference detected", "Field required": "Field required", "Field is frozen": "Field is frozen", "Instance is frozen": "Instance is frozen", "Extra inputs are not permitted": "Extra inputs are not permitted", "Keys should be strings": "Keys should be strings", "Error extracting attribute: {}": "Error extracting attribute: {}", "Input should be a valid dictionary or instance of {}": "Input should be a valid dictionary or instance of {}", "Input should be a valid dictionary or object to extract fields from": "Input should be a valid dictionary or object to extract fields from", "Input should be a dictionary or an instance of {}": "Input should be a dictionary or an instance of {}", "Input should be an instance of {}": "Input should be an instance of {}", "Input should be None": "Input should be None", "Input should be greater than {}": "Input should be greater than {}", "Input should be greater than or equal to {}": "Input should be greater than or equal to {}", "Input should be less than {}": "Input should be less than {}", "Input should be less than or equal to {}": "Input should be less than or equal to {}", "Input should be a multiple of {}": "Input should be a multiple of {}", "Input should be a finite number": "Input should be a finite number", "Input should be iterable": "Input should be iterable", "Error iterating over object, error: {}": "Error iterating over object, error: {}", "Input should be a valid string": "Input should be a valid string", "Input should be a string, not an instance of a subclass of str": "Input should be a string, not an instance of a subclass of str", "Input should be a valid string, unable to parse raw data as a unicode string": "Input should be a valid string, unable to parse raw data as a unicode string", "String should have at least {}": "String should have at least {}", "String should have at most {}": "String should have at most {}", "String should match pattern '{}'": "String should match pattern '{}'", "Input should be {}": "Input should be {}", "Input should be a valid dictionary": "Input should be a valid dictionary", "Input should be a valid mapping, error: {}": "Input should be a valid mapping, error: {}", "Input should be a valid list": "Input should be a valid list", "Input should be a valid tuple": "Input should be a valid tuple", "Input should be a valid set": "Input should be a valid set", "Input should be a valid boolean": "Input should be a valid boolean", "Input should be a valid boolean, unable to interpret input": "Input should be a valid boolean, unable to interpret input", "Input should be a valid integer": "Input should be a valid integer", "Input should be a valid integer, unable to parse string as an integer": "Input should be a valid integer, unable to parse string as an integer", "Unable to parse input string as an integer, exceeded maximum size": "Unable to parse input string as an integer, exceeded maximum size", "Input should be a valid integer, got a number with a fractional part": "Input should be a valid integer, got a number with a fractional part", "Input should be a valid number": "Input should be a valid number", "Input should be a valid number, unable to parse string as a number": "Input should be a valid number, unable to parse string as a number", "Input should be a valid bytes": "Input should be a valid bytes", "Data should have at least {}": "Data should have at least {}", "Data should have at most {}": "Data should have at most {}", "Data should be valid {}": "Data should be valid {}", "Value error, {}": "Value error, {}", "Assertion failed, {}": "<PERSON><PERSON><PERSON> failed, {}", "Input should be a valid date": "Input should be a valid date", "Input should be a valid date in the format YYYY-MM-DD, {}": "Input should be a valid date in the format YYYY-MM-DD, {}", "Input should be a valid date or datetime, {}": "Input should be a valid date or datetime, {}", "Datetimes provided to dates should have zero time - e.g. be exact dates": "Datetimes provided to dates should have zero time - e.g. be exact dates", "Date should be in the past": "Date should be in the past", "Date should be in the future": "Date should be in the future", "Input should be a valid time": "Input should be a valid time", "Input should be in a valid time format, {}": "Input should be in a valid time format, {}", "Input should be a valid datetime": "Input should be a valid datetime", "Input should be a valid datetime, {}": "Input should be a valid datetime, {}", "Invalid datetime object, got {}": "Invalid datetime object, got {}", "Input should be a valid datetime or date, {}": "Input should be a valid datetime or date, {}", "Input should be in the past": "Input should be in the past", "Input should be in the future": "Input should be in the future", "Input should not have timezone info": "Input should not have timezone info", "Input should have timezone info": "Input should have timezone info", "Timezone offset of {}": "Timezone offset of {}", "Input should be a valid timedelta": "Input should be a valid timedel<PERSON>", "Input should be a valid timedelta, {}": "Input should be a valid <PERSON><PERSON><PERSON>, {}", "Input should be a valid frozenset": "Input should be a valid frozenset", "Input should be a subclass of {}": "Input should be a subclass of {}", "Input should be callable": "Input should be callable", "Input tag '{}": "Input tag '{}", "Unable to extract tag using discriminator {}": "Unable to extract tag using discriminator {}", "Arguments must be a tuple, list or a dictionary": "Arguments must be a tuple, list or a dictionary", "Missing required argument": "Missing required argument", "Unexpected keyword argument": "Unexpected keyword argument", "Missing required keyword only argument": "Missing required keyword only argument", "Unexpected positional argument": "Unexpected positional argument", "Missing required positional only argument": "Missing required positional only argument", "Got multiple values for argument": "Got multiple values for argument", "URL input should be a string or URL": "URL input should be a string or URL", "Input should be a valid URL, {}": "Input should be a valid URL, {}", "Input violated strict URL syntax rules, {}": "Input violated strict URL syntax rules, {}", "URL should have at most {}": "URL should have at most {}", "URL scheme should be {}": "URL scheme should be {}", "UUID input should be a string, bytes or UUID object": "UUID input should be a string, bytes or UUID object", "Input should be a valid UUID, {}": "Input should be a valid UUID, {}", "UUID version {} expected": "UUID version {} expected", "Decimal input should be an integer, float, string or Decimal object": "Decimal input should be an integer, float, string or Decimal object", "Input should be a valid decimal": "Input should be a valid decimal", "Decimal input should have no more than {} in total": "Decimal input should have no more than {} in total", "Decimal input should have no more than {}": "Decimal input should have no more than {}", "Decimal input should have no more than {} before the decimal point": "Decimal input should have no more than {} before the decimal point", "Input should be a valid python complex object, a number, or a valid complex string following the rules at https://docs.python.org/3/library/functions.html#complex": "Input should be a valid python complex object, a number, or a valid complex string following the rules at https://docs.python.org/3/library/functions.html#complex", "Input should be a valid complex string following the rules at https://docs.python.org/3/library/functions.html#complex": "Input should be a valid complex string following the rules at https://docs.python.org/3/library/functions.html#complex"}