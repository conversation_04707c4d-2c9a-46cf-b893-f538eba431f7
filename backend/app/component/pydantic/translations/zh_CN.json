{"Object has no attribute '{}'": "对象没有属性'{}'", "Invalid JSON: {}": "无效的 JSON：{}", "JSON input should be string, bytes or bytearray": "JSON 输入应为字符串、字节或字节数组", "Cannot check `{}` when validating from json, use a JsonOrPython validator instead": "在从 JSON 验证时，无法检查`{}`，请改用 JsonOrPython 验证器", "Recursion error - cyclic reference detected": "递归错误 - 检测到循环引用", "Field required": "字段必填", "Field is frozen": "字段已冻结", "Instance is frozen": "实例已冻结", "Extra inputs are not permitted": "不允许额外输入", "Keys should be strings": "键应为字符串", "Error extracting attribute: {}": "提取属性时出错：{}", "Input should be a valid dictionary or instance of {}": "输入应为有效的字典或{}的实例", "Input should be a valid dictionary or object to extract fields from": "输入应为有效的字典或可用于提取字段的对象", "Input should be a dictionary or an instance of {}": "输入应为字典或{}的实例", "Input should be an instance of {}": "输入应为{}的实例", "Input should be None": "输入应为 None", "Input should be greater than {}": "输入应大于{}", "Input should be greater than or equal to {}": "输入应大于或等于{}", "Input should be less than {}": "输入应小于{}", "Input should be less than or equal to {}": "输入应小于或等于{}", "Input should be a multiple of {}": "输入应为{}的倍数", "Input should be a finite number": "输入应为有限数字", "Input should be iterable": "输入应为可迭代对象", "Error iterating over object, error: {}": "迭代对象时出错，错误：{}", "Input should be a valid string": "输入应为有效字符串", "Input should be a string, not an instance of a subclass of str": "输入应为字符串，而不是 str 的子类实例", "Input should be a valid string, unable to parse raw data as a unicode string": "输入应为有效字符串，无法将原始数据解析为 Unicode 字符串", "String should have at least {}": "字符串应至少有{}", "String should have at most {}": "字符串应最多有{}", "String should match pattern '{}'": "字符串应匹配模式'{}'", "Input should be {}": "输入应为{}", "Input should be a valid dictionary": "输入应为有效的字典", "Input should be a valid mapping, error: {}": "输入应为有效的映射，错误：{}", "Input should be a valid list": "输入应为有效的列表", "Input should be a valid tuple": "输入应为有效的元组", "Input should be a valid set": "输入应为有效的集合", "Input should be a valid boolean": "输入应为有效的布尔值", "Input should be a valid boolean, unable to interpret input": "输入应为有效的布尔值，无法解析输入", "Input should be a valid integer": "输入应为有效的整数", "Input should be a valid integer, unable to parse string as an integer": "输入应为有效的整数，无法将字符串解析为整数", "Unable to parse input string as an integer, exceeded maximum size": "无法将输入字符串解析为整数，超出最大尺寸", "Input should be a valid integer, got a number with a fractional part": "输入应为有效的整数，但输入的数字有小数部分", "Input should be a valid number": "输入应为有效的数字", "Input should be a valid number, unable to parse string as a number": "输入应为有效的数字，无法将字符串解析为数字", "Input should be a valid bytes": "输入应为有效的字节", "Data should have at least {}": "数据应至少有{}", "Data should have at most {}": "数据应最多有{}", "Data should be valid {}": "数据应为有效的{}", "Value error, {}": "值错误，{}", "Assertion failed, {}": "断言失败，{}", "Input should be a valid date": "输入应为有效的日期", "Input should be a valid date in the format YYYY-MM-DD, {}": "输入应为有效的日期，格式为 YYYY-MM-DD，{}", "Input should be a valid date or datetime, {}": "输入应为有效的日期或日期时间，{}", "Datetimes provided to dates should have zero time - e.g. be exact dates": "提供给日期的日期时间应为零时间，即为精确日期", "Date should be in the past": "日期应为过去的日期", "Date should be in the future": "日期应为未来的日期", "Input should be a valid time": "输入应为有效的时间", "Input should be in a valid time format, {}": "输入应为有效的时间格式，{}", "Input should be a valid datetime": "输入应为有效的日期时间", "Input should be a valid datetime, {}": "输入应为有效的日期时间，{}", "Invalid datetime object, got {}": "无效的日期时间对象，得到{}", "Input should be a valid datetime or date, {}": "输入应为有效的日期时间或日期，{}", "Input should be in the past": "输入应为过去的日期/时间", "Input should be in the future": "输入应为未来的日期/时间", "Input should not have timezone info": "输入不应包含时区信息", "Input should have timezone info": "输入应包含时区信息", "Timezone offset of {}": "时区偏移量为{}", "Input should be a valid timedelta": "输入应为有效的 timed<PERSON>ta", "Input should be a valid timedelta, {}": "输入应为有效的 <PERSON><PERSON><PERSON>，{}", "Input should be a valid frozenset": "输入应为有效的 frozenset", "Input should be a subclass of {}": "输入应为{}的子类", "Input should be callable": "输入应为可调用对象", "Input tag '{}": "输入标签'{}'", "Unable to extract tag using discriminator {}": "无法使用区分符{}提取标签", "Arguments must be a tuple, list or a dictionary": "参数必须是元组、列表或字典", "Missing required argument": "缺少必需的参数", "Unexpected keyword argument": "意外的关键字参数", "Missing required keyword only argument": "缺少必需的关键字参数", "Unexpected positional argument": "意外的位置参数", "Missing required positional only argument": "缺少必需的位置参数", "Got multiple values for argument": "为参数提供了多个值", "URL input should be a string or URL": "URL 输入应为字符串或 URL", "Input should be a valid URL, {}": "输入应为有效的 URL，{}", "Input violated strict URL syntax rules, {}": "输入违反了严格的 URL 语法规则，{}", "URL should have at most {}": "URL 应最多有{}", "URL scheme should be {}": "URL 的协议应为{}", "UUID input should be a string, bytes or UUID object": "UUID 输入应为字符串、字节或 UUID 对象", "Input should be a valid UUID, {}": "输入应为有效的 UUID，{}", "UUID version {} expected": "期望的 UUID 版本为{}", "Decimal input should be an integer, float, string or Decimal object": "十进制输入应为整数、浮点数、字符串或 Decimal 对象", "Input should be a valid decimal": "输入应为有效的十进制数", "Decimal input should have no more than {} in total": "十进制输入的总位数不应超过{}", "Decimal input should have no more than {}": "十进制输入不应超过{}", "Decimal input should have no more than {} before the decimal point": "十进制输入的小数点前不应超过{}位", "Input should be a valid python complex object, a number, or a valid complex string following the rules at https://docs.python.org/3/library/functions.html#complex": "输入应为有效的 Python 复杂对象、数字，或遵循 https://docs.python.org/3/library/functions.html#complex 规则的有效复杂字符串", "Input should be a valid complex string following the rules at https://docs.python.org/3/library/functions.html#complex": "输入应为遵循 https://docs.python.org/3/library/functions.html#complex 规则的有效复杂字符串"}