"""
电商需求洞察应用 - API控制器
处理电商分析相关的HTTP请求
"""

import asyncio
import uuid
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Response, BackgroundTasks
from fastapi.responses import StreamingResponse
from loguru import logger

from app.model.ecommerce import (
    StartEcommerceTaskRequest,
    StartEcommerceTaskResponse,
    TaskStatusResponse,
    ConfirmKeywordsRequest,
    ExportRequest,
    ExportResponse,
    LoginStatusResult,
    EcommerceTaskData,
    EcommerceTaskStatus,
    create_task_progress
)
from app.service.ecommerce_service import (
    EcommerceTaskService,
    PlatformLoginService,
    DataExportService
)
from app.service.task import get_task_lock, create_task_lock
from app.exception.exception import UserException
from app.component import code


router = APIRouter(tags=["ecommerce"], prefix="/ecommerce")

# 全局任务服务实例
task_service = EcommerceTaskService()
login_service = PlatformLoginService()
export_service = DataExportService()


@router.post("/task/start", response_model=StartEcommerceTaskResponse, name="启动电商分析任务")
async def start_ecommerce_task(request: StartEcommerceTaskRequest) -> StartEcommerceTaskResponse:
    """
    启动新的电商分析任务
    """
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务锁
        task_lock = create_task_lock(task_id)
        
        # 创建任务数据
        task_data = EcommerceTaskData(
            task_id=task_id,
            scan_mode=request.scan_mode,
            keyword_input=request.keyword_input,
            progress=create_task_progress(EcommerceTaskStatus.pending, "任务已创建，等待开始")
        )
        
        # 保存任务数据
        await task_service.create_task(task_data)
        
        # 启动后台任务处理
        asyncio.create_task(task_service.process_task(task_id))
        
        logger.info(f"电商分析任务已启动: {task_id}")
        
        return StartEcommerceTaskResponse(
            task_id=task_id,
            status=EcommerceTaskStatus.pending
        )
        
    except Exception as e:
        logger.error(f"启动电商分析任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")


@router.get("/task/{task_id}/status", response_model=TaskStatusResponse, name="查询任务状态")
async def get_task_status(task_id: str) -> TaskStatusResponse:
    """
    查询电商分析任务状态
    """
    try:
        task_data = await task_service.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 构建响应数据
        response_data = None
        if task_data.progress.status in [EcommerceTaskStatus.user_confirming, EcommerceTaskStatus.completed]:
            response_data = {
                "processed_keywords": task_data.processed_keywords.dict() if task_data.processed_keywords else None,
                "link_discovery": task_data.link_discovery.dict() if task_data.link_discovery else None,
                "comment_collection": task_data.comment_collection.dict() if task_data.comment_collection else None,
                "analysis": task_data.analysis.dict() if task_data.analysis else None
            }
        
        return TaskStatusResponse(
            task_id=task_id,
            status=task_data.progress.status,
            progress=task_data.progress,
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.post("/task/{task_id}/confirm-keywords", name="确认关键词")
async def confirm_keywords(task_id: str, request: ConfirmKeywordsRequest):
    """
    用户确认处理后的关键词
    """
    try:
        # 验证任务存在且状态正确
        task_data = await task_service.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_data.progress.status != EcommerceTaskStatus.user_confirming:
            raise HTTPException(status_code=400, detail="任务状态不允许确认关键词")
        
        # 更新确认的关键词
        await task_service.confirm_keywords(task_id, request.confirmed_keywords)
        
        logger.info(f"关键词已确认: {task_id}, 关键词: {request.confirmed_keywords}")
        
        return Response(status_code=200)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认关键词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"确认失败: {str(e)}")


@router.post("/task/{task_id}/pause", name="暂停任务")
async def pause_task(task_id: str):
    """
    暂停电商分析任务
    """
    try:
        await task_service.pause_task(task_id)
        logger.info(f"任务已暂停: {task_id}")
        return Response(status_code=200)
        
    except Exception as e:
        logger.error(f"暂停任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停失败: {str(e)}")


@router.post("/task/{task_id}/resume", name="恢复任务")
async def resume_task(task_id: str):
    """
    恢复电商分析任务
    """
    try:
        await task_service.resume_task(task_id)
        logger.info(f"任务已恢复: {task_id}")
        return Response(status_code=200)
        
    except Exception as e:
        logger.error(f"恢复任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"恢复失败: {str(e)}")


@router.delete("/task/{task_id}", name="删除任务")
async def delete_task(task_id: str):
    """
    删除电商分析任务
    """
    try:
        await task_service.delete_task(task_id)
        logger.info(f"任务已删除: {task_id}")
        return Response(status_code=204)
        
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/task/{task_id}/export", response_model=ExportResponse, name="导出任务数据")
async def export_task_data(task_id: str, request: ExportRequest):
    """
    导出电商分析任务数据
    """
    try:
        # 验证任务存在
        task_data = await task_service.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 执行导出
        export_result = await export_service.export_data(task_data, request)
        
        logger.info(f"数据导出成功: {task_id}, 文件: {export_result.filename}")
        
        return export_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@router.get("/login/status", response_model=LoginStatusResult, name="检查平台登录状态")
async def check_login_status() -> LoginStatusResult:
    """
    检查淘宝和小红书的登录状态
    """
    try:
        login_status = await login_service.check_all_platforms()
        return login_status
        
    except Exception as e:
        logger.error(f"检查登录状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/login/{platform}/start", name="启动平台登录")
async def start_platform_login(platform: str):
    """
    启动指定平台的登录流程
    """
    try:
        if platform not in ["taobao", "xiaohongshu"]:
            raise HTTPException(status_code=400, detail="不支持的平台")

        login_url = await login_service.start_login(platform)

        return {"login_url": login_url, "message": f"请在浏览器中完成{platform}登录"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动{platform}登录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动登录失败: {str(e)}")


@router.post("/login/save", name="保存登录会话")
async def save_login_session(session_data: dict):
    """
    保存平台登录会话数据
    """
    try:
        success = await login_service.save_login_session(session_data)
        if success:
            return {"success": True, "message": "登录会话已保存"}
        else:
            raise HTTPException(status_code=500, detail="保存登录会话失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存登录会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")


@router.get("/tasks", name="获取任务列表")
async def get_task_list(limit: int = 20, offset: int = 0):
    """
    获取电商分析任务列表
    """
    try:
        tasks = await task_service.get_task_list(limit=limit, offset=offset)
        return {
            "tasks": [
                {
                    "task_id": task.task_id,
                    "scan_mode": task.scan_mode,
                    "status": task.progress.status,
                    "progress": task.progress.progress,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at
                }
                for task in tasks
            ],
            "total": len(tasks)
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/task/{task_id}/stream", name="任务状态流")
async def task_status_stream(task_id: str):
    """
    实时推送任务状态更新（SSE）
    """
    async def event_generator():
        try:
            while True:
                task_data = await task_service.get_task(task_id)
                if not task_data:
                    yield f"data: {{'error': '任务不存在'}}\n\n"
                    break
                
                # 构建SSE数据
                sse_data = {
                    "task_id": task_id,
                    "status": task_data.progress.status,
                    "progress": task_data.progress.progress,
                    "current_step": task_data.progress.current_step,
                    "timestamp": task_data.updated_at.isoformat()
                }
                
                yield f"data: {sse_data}\n\n"
                
                # 如果任务完成或失败，结束流
                if task_data.progress.status in [EcommerceTaskStatus.completed, EcommerceTaskStatus.failed]:
                    break
                
                # 等待1秒后再次检查
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"任务状态流错误: {str(e)}")
            yield f"data: {{'error': '{str(e)}'}}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )
