"""
电商需求洞察应用 - 后端数据模型定义
基于Pydantic的数据验证和序列化模型
"""

from enum import Enum
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime
from pydantic import BaseModel, Field, validator
import json


# ==================== 枚举定义 ====================
class ScanMode(str, Enum):
    """扫描模式"""
    quick = "quick"
    deep = "deep"


class Platform(str, Enum):
    """平台类型"""
    taobao = "taobao"
    xiaohongshu = "xiaohongshu"


class EcommerceTaskStatus(str, Enum):
    """电商分析任务状态"""
    pending = "pending"
    keyword_processing = "keyword_processing"
    user_confirming = "user_confirming"
    link_discovering = "link_discovering"
    link_deduplicating = "link_deduplicating"
    comment_collecting = "comment_collecting"
    ai_analyzing = "ai_analyzing"
    completed = "completed"
    failed = "failed"
    paused = "paused"


class ExportFormat(str, Enum):
    """导出格式"""
    markdown = "markdown"
    csv = "csv"
    excel = "excel"


class ExportDataType(str, Enum):
    """导出数据类型"""
    links = "links"
    comments = "comments"
    analysis = "analysis"
    report = "report"


# ==================== 基础数据模型 ====================
class KeywordInput(BaseModel):
    """关键词输入数据"""
    mode: Literal["manual", "file"]
    content: str = Field(..., description="手动输入的关键词字符串")
    file_path: Optional[str] = Field(None, description="上传文件的路径")
    exclude_words: Optional[List[str]] = Field(default_factory=list, description="排除词列表")


class KeywordCluster(BaseModel):
    """关键词聚类结果"""
    theme: str = Field(..., description="主题名称")
    keywords: List[str] = Field(..., description="该主题下的关键词")


class ProcessedKeywords(BaseModel):
    """处理后的关键词数据"""
    original: List[str] = Field(..., description="原始关键词")
    expanded: Optional[List[str]] = Field(None, description="扩展后的关键词（少于5个时）")
    clustered: Optional[List[KeywordCluster]] = Field(None, description="聚类结果（5个以上时）")
    final: List[str] = Field(..., description="最终确认的关键词")


class DiscoveredLink(BaseModel):
    """发现的链接数据"""
    url: str = Field(..., description="链接URL")
    platform: Platform = Field(..., description="平台类型")
    keyword: str = Field(..., description="对应的搜索关键词")
    sales: Optional[int] = Field(None, description="淘宝销量")
    likes: Optional[int] = Field(None, description="小红书点赞数")
    title: Optional[str] = Field(None, description="商品/帖子标题")
    discovered_at: datetime = Field(default_factory=datetime.now, description="发现时间")


class LinkDiscoveryResult(BaseModel):
    """链接发现结果"""
    taobao: List[DiscoveredLink] = Field(default_factory=list)
    xiaohongshu: List[DiscoveredLink] = Field(default_factory=list)
    total: int = Field(0, description="总链接数")
    duplicates_removed: int = Field(0, description="去重数量")


class CollectedComment(BaseModel):
    """采集的评论数据"""
    url: str = Field(..., description="链接URL")
    platform: Platform = Field(..., description="平台类型")
    comments: List[str] = Field(..., description="评论文本数组")
    collected_at: datetime = Field(default_factory=datetime.now, description="采集时间")
    comment_count: int = Field(0, description="采集到的评论数量")

    @validator('comment_count', always=True)
    def set_comment_count(cls, v, values):
        """自动设置评论数量"""
        if 'comments' in values:
            return len(values['comments'])
        return v


class CommentCollectionResult(BaseModel):
    """评论采集结果"""
    links: List[CollectedComment] = Field(default_factory=list)
    total_comments: int = Field(0, description="总评论数")
    successful_links: int = Field(0, description="成功采集的链接数")
    failed_links: int = Field(0, description="失败的链接数")


class ProductAnalysis(BaseModel):
    """单个产品分析结果"""
    url: str = Field(..., description="产品链接")
    platform: Platform = Field(..., description="平台类型")
    advantages: List[str] = Field(default_factory=list, max_items=3, description="产品优点（最多3个）")
    disadvantages: List[str] = Field(default_factory=list, max_items=3, description="产品缺点（最多3个）")
    opportunities: List[str] = Field(default_factory=list, max_items=2, description="潜在机会点（最多2个）")


class MarketOpportunity(BaseModel):
    """市场机会洞察"""
    title: str = Field(..., description="机会点标题")
    description: str = Field(..., description="详细描述")
    evidence: List[str] = Field(..., description="支撑证据（原始评论）")
    priority: Optional[Literal["high", "medium", "low"]] = Field(None, description="优先级")


class AnalysisResult(BaseModel):
    """AI分析结果"""
    map_results: List[ProductAnalysis] = Field(default_factory=list, description="Map阶段结果")
    opportunities: List[MarketOpportunity] = Field(default_factory=list, description="Reduce阶段结果")
    summary: str = Field("", description="分析总结")
    analyzed_at: datetime = Field(default_factory=datetime.now, description="分析时间")


class EcommerceTaskProgress(BaseModel):
    """任务进度信息"""
    status: EcommerceTaskStatus = Field(..., description="任务状态")
    current_step: str = Field("", description="当前步骤描述")
    progress: int = Field(0, ge=0, le=100, description="进度百分比 0-100")
    details: Optional[Dict[str, int]] = Field(default_factory=dict, description="详细进度信息")


class EcommerceTaskData(BaseModel):
    """电商分析任务完整数据"""
    task_id: str = Field(..., description="任务ID")
    scan_mode: ScanMode = Field(..., description="扫描模式")
    keyword_input: KeywordInput = Field(..., description="关键词输入")
    processed_keywords: Optional[ProcessedKeywords] = Field(None, description="处理后的关键词")
    link_discovery: Optional[LinkDiscoveryResult] = Field(None, description="链接发现结果")
    comment_collection: Optional[CommentCollectionResult] = Field(None, description="评论采集结果")
    analysis: Optional[AnalysisResult] = Field(None, description="AI分析结果")
    progress: EcommerceTaskProgress = Field(..., description="任务进度")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error: Optional[str] = Field(None, description="错误信息")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# ==================== API请求/响应模型 ====================
class StartEcommerceTaskRequest(BaseModel):
    """启动电商分析任务请求"""
    scan_mode: ScanMode = Field(..., description="扫描模式")
    keyword_input: KeywordInput = Field(..., description="关键词输入")


class StartEcommerceTaskResponse(BaseModel):
    """启动电商分析任务响应"""
    task_id: str = Field(..., description="任务ID")
    status: EcommerceTaskStatus = Field(..., description="任务状态")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应"""
    task_id: str = Field(..., description="任务ID")
    status: EcommerceTaskStatus = Field(..., description="任务状态")
    progress: EcommerceTaskProgress = Field(..., description="任务进度")
    data: Optional[Dict[str, Any]] = Field(None, description="部分任务数据")


class ConfirmKeywordsRequest(BaseModel):
    """确认关键词请求"""
    task_id: str = Field(..., description="任务ID")
    confirmed_keywords: List[str] = Field(..., description="确认的关键词列表")


class ExportRequest(BaseModel):
    """数据导出请求"""
    task_id: str = Field(..., description="任务ID")
    format: ExportFormat = Field(..., description="导出格式")
    data_type: ExportDataType = Field(..., description="导出数据类型")
    filename: Optional[str] = Field(None, description="自定义文件名")


class ExportResponse(BaseModel):
    """数据导出响应"""
    success: bool = Field(..., description="是否成功")
    filename: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件路径")
    size: int = Field(..., description="文件大小（字节）")
    error: Optional[str] = Field(None, description="错误信息")


# ==================== 平台登录相关 ====================
class PlatformLoginStatus(BaseModel):
    """平台登录状态"""
    platform: Platform = Field(..., description="平台类型")
    is_logged_in: bool = Field(..., description="是否已登录")
    session_valid: bool = Field(..., description="会话是否有效")
    file_exists: bool = Field(..., description="登录文件是否存在")
    last_login_time: Optional[datetime] = Field(None, description="最后登录时间")
    error: Optional[str] = Field(None, description="错误信息")


class LoginStatusResult(BaseModel):
    """登录状态检查结果"""
    taobao: PlatformLoginStatus = Field(..., description="淘宝登录状态")
    xiaohongshu: PlatformLoginStatus = Field(..., description="小红书登录状态")
    last_checked: datetime = Field(default_factory=datetime.now, description="最后检查时间")


# ==================== 智能体相关模型 ====================
class EcommerceAgentStatus(BaseModel):
    """电商智能体状态"""
    phase: Optional[str] = Field(None, description="当前阶段")
    current_keyword: Optional[str] = Field(None, description="当前处理的关键词")
    processed_count: int = Field(0, description="已处理数量")
    total_count: int = Field(0, description="总数量")


# ==================== 工具函数 ====================
def create_task_progress(status: EcommerceTaskStatus, step: str = "", progress: int = 0) -> EcommerceTaskProgress:
    """创建任务进度对象"""
    return EcommerceTaskProgress(
        status=status,
        current_step=step,
        progress=progress
    )


def update_task_data(task_data: EcommerceTaskData, **kwargs) -> EcommerceTaskData:
    """更新任务数据"""
    update_dict = kwargs.copy()
    update_dict['updated_at'] = datetime.now()
    
    for key, value in update_dict.items():
        if hasattr(task_data, key):
            setattr(task_data, key, value)
    
    return task_data
