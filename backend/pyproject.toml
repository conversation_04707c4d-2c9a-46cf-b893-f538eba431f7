[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "==3.10.16"
dependencies = [
    "camel-ai[eigent]>=0.2.74a5",
    "fastapi>=0.115.12",
    "fastapi-babel>=1.0.0",
    "uvicorn[standard]>=0.34.2",
    "pydantic-i18n>=0.4.5",
    "python-dotenv>=1.1.0",
    "httpx[socks]>=0.28.1",
    "loguru>=0.7.3",
    "pydash>=8.0.5",
    "inflection>=0.5.1",
    "aiofiles>=24.1.0",
]


[dependency-groups]
dev = ["babel>=2.17.0"]

[tool.ruff]
line-length = 120

[tool.ruff.lint]
extend-select = [
    "B006", # forbid def demo(mutation = [])
]
