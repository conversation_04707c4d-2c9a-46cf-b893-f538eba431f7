/**
 * 登录引导组件
 * 在应用启动时检查平台登录状态，引导用户登录
 */

import React, { useState, useEffect } from 'react';
import { 
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowTopRightOnSquareIcon,
  RefreshIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { ecommerceAPI } from '../../api/ecommerce';

interface LoginGuideProps {
  onComplete: () => void;
  onSkip: () => void;
}

const LoginGuide: React.FC<LoginGuideProps> = ({ onComplete, onSkip }) => {
  const [loginStatus, setLoginStatus] = useState<LoginStatusResult | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const [isLoggingIn, setIsLoggingIn] = useState<string | null>(null);

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = async () => {
    setIsChecking(true);
    try {
      const status = await ecommerceAPI.checkLoginStatus();
      setLoginStatus(status);
    } catch (error) {
      console.error('检查登录状态失败:', error);
      toast.error('检查登录状态失败');
    } finally {
      setIsChecking(false);
    }
  };

  const handleLogin = async (platform: 'taobao' | 'xiaohongshu') => {
    setIsLoggingIn(platform);
    try {
      const response = await ecommerceAPI.startPlatformLogin(platform);
      
      // 打开登录窗口
      const loginWindow = window.open(response.login_url, '_blank', 'width=800,height=600');
      
      // 监听登录完成
      const checkLoginComplete = () => {
        if (loginWindow?.closed) {
          // 窗口关闭，重新检查登录状态
          setTimeout(() => {
            checkLoginStatus();
            setIsLoggingIn(null);
          }, 1000);
        } else {
          setTimeout(checkLoginComplete, 1000);
        }
      };
      
      setTimeout(checkLoginComplete, 2000);
      
      toast.success(response.message);
    } catch (error) {
      console.error(`启动${platform}登录失败:`, error);
      toast.error(`启动${platform}登录失败`);
      setIsLoggingIn(null);
    }
  };

  const getPlatformIcon = (platform: 'taobao' | 'xiaohongshu') => {
    if (platform === 'taobao') {
      return (
        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
          <span className="text-orange-600 font-bold text-lg">淘</span>
        </div>
      );
    } else {
      return (
        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <span className="text-red-600 font-bold text-lg">小</span>
        </div>
      );
    }
  };

  const getStatusIcon = (status: PlatformLoginStatus) => {
    if (status.is_logged_in && status.session_valid) {
      return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
    } else if (status.is_logged_in && !status.session_valid) {
      return <ExclamationTriangleIcon className="w-6 h-6 text-yellow-500" />;
    } else {
      return <XCircleIcon className="w-6 h-6 text-red-500" />;
    }
  };

  const getStatusText = (status: PlatformLoginStatus) => {
    if (status.is_logged_in && status.session_valid) {
      return '已登录';
    } else if (status.is_logged_in && !status.session_valid) {
      return '会话过期';
    } else {
      return '未登录';
    }
  };

  const isAnyPlatformLoggedIn = loginStatus && 
    ((loginStatus.taobao.is_logged_in && loginStatus.taobao.session_valid) ||
     (loginStatus.xiaohongshu.is_logged_in && loginStatus.xiaohongshu.session_valid));

  if (isChecking) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              检查登录状态
            </h2>
            <p className="text-gray-600">
              正在检查淘宝和小红书的登录状态...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-lg w-full mx-4">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            平台登录设置
          </h2>
          <p className="text-gray-600">
            为了获取更准确的商品信息和用户评论，建议先登录相关平台账号
          </p>
        </div>

        {/* 登录状态列表 */}
        <div className="space-y-4 mb-6">
          {/* 淘宝 */}
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-4">
              {getPlatformIcon('taobao')}
              <div>
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-medium text-gray-900">淘宝</h3>
                  {loginStatus && getStatusIcon(loginStatus.taobao)}
                  <span className={`text-sm ${
                    loginStatus?.taobao.is_logged_in && loginStatus?.taobao.session_valid
                      ? 'text-green-600'
                      : loginStatus?.taobao.is_logged_in && !loginStatus?.taobao.session_valid
                      ? 'text-yellow-600'
                      : 'text-red-600'
                  }`}>
                    {loginStatus ? getStatusText(loginStatus.taobao) : '检查中...'}
                  </span>
                </div>
                {loginStatus?.taobao.last_login_time && (
                  <p className="text-xs text-gray-500">
                    上次登录：{new Date(loginStatus.taobao.last_login_time).toLocaleString()}
                  </p>
                )}
                {loginStatus?.taobao.error && (
                  <p className="text-xs text-red-500">
                    {loginStatus.taobao.error}
                  </p>
                )}
              </div>
            </div>
            
            {loginStatus && (!loginStatus.taobao.is_logged_in || !loginStatus.taobao.session_valid) && (
              <button
                onClick={() => handleLogin('taobao')}
                disabled={isLoggingIn === 'taobao'}
                className="inline-flex items-center px-4 py-2 border border-orange-300 text-sm font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoggingIn === 'taobao' ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600 mr-2"></div>
                    登录中...
                  </>
                ) : (
                  <>
                    <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />
                    登录
                  </>
                )}
              </button>
            )}
          </div>

          {/* 小红书 */}
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-4">
              {getPlatformIcon('xiaohongshu')}
              <div>
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-medium text-gray-900">小红书</h3>
                  {loginStatus && getStatusIcon(loginStatus.xiaohongshu)}
                  <span className={`text-sm ${
                    loginStatus?.xiaohongshu.is_logged_in && loginStatus?.xiaohongshu.session_valid
                      ? 'text-green-600'
                      : loginStatus?.xiaohongshu.is_logged_in && !loginStatus?.xiaohongshu.session_valid
                      ? 'text-yellow-600'
                      : 'text-red-600'
                  }`}>
                    {loginStatus ? getStatusText(loginStatus.xiaohongshu) : '检查中...'}
                  </span>
                </div>
                {loginStatus?.xiaohongshu.last_login_time && (
                  <p className="text-xs text-gray-500">
                    上次登录：{new Date(loginStatus.xiaohongshu.last_login_time).toLocaleString()}
                  </p>
                )}
                {loginStatus?.xiaohongshu.error && (
                  <p className="text-xs text-red-500">
                    {loginStatus.xiaohongshu.error}
                  </p>
                )}
              </div>
            </div>
            
            {loginStatus && (!loginStatus.xiaohongshu.is_logged_in || !loginStatus.xiaohongshu.session_valid) && (
              <button
                onClick={() => handleLogin('xiaohongshu')}
                disabled={isLoggingIn === 'xiaohongshu'}
                className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoggingIn === 'xiaohongshu' ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                    登录中...
                  </>
                ) : (
                  <>
                    <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />
                    登录
                  </>
                )}
              </button>
            )}
          </div>
        </div>

        {/* 提示信息 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">温馨提示：</p>
              <ul className="list-disc list-inside space-y-1">
                <li>登录后可以获取更多商品信息和用户评论</li>
                <li>至少需要登录一个平台才能进行完整分析</li>
                <li>登录状态会自动保存，下次启动时无需重新登录</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between">
          <button
            onClick={checkLoginStatus}
            disabled={isChecking}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshIcon className="w-4 h-4 mr-2" />
            刷新状态
          </button>
          
          <div className="space-x-3">
            <button
              onClick={onSkip}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              跳过
            </button>
            <button
              onClick={onComplete}
              disabled={!isAnyPlatformLoggedIn}
              className={`px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${
                isAnyPlatformLoggedIn
                  ? 'bg-blue-600 hover:bg-blue-700'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              完成设置
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginGuide;
