/**
 * 任务监控组件
 * 实时显示任务进度和状态
 */

import React, { useState, useEffect } from 'react';
import { 
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon,
  ArrowDownTrayIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { formatTaskStatus, formatScanMode, getProgressColor, generateExportFilename } from '../../api/ecommerce';

interface TaskMonitorProps {
  taskId: string;
  taskData: EcommerceTaskData;
  onExport: (request: ExportRequest) => void;
}

const TaskMonitor: React.FC<TaskMonitorProps> = ({
  taskId,
  taskData,
  onExport
}) => {
  const [showKeywordConfirm, setShowKeywordConfirm] = useState(false);
  const [confirmedKeywords, setConfirmedKeywords] = useState<string[]>([]);

  // 检查是否需要用户确认关键词
  useEffect(() => {
    if (taskData.progress.status === 'user_confirming' && taskData.processedKeywords) {
      setShowKeywordConfirm(true);
      // 设置默认选中的关键词
      if (taskData.processedKeywords.clustered) {
        const defaultKeywords: string[] = [];
        taskData.processedKeywords.clustered.forEach(cluster => {
          defaultKeywords.push(...cluster.keywords.slice(0, 3));
        });
        setConfirmedKeywords(defaultKeywords);
      } else if (taskData.processedKeywords.expanded) {
        setConfirmedKeywords(taskData.processedKeywords.expanded);
      }
    }
  }, [taskData.progress.status, taskData.processedKeywords]);

  const getStatusIcon = (status: EcommerceTaskStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-6 h-6 text-red-500" />;
      case 'paused':
        return <PauseIcon className="w-6 h-6 text-yellow-500" />;
      case 'user_confirming':
        return <ExclamationCircleIcon className="w-6 h-6 text-blue-500" />;
      default:
        return <ClockIcon className="w-6 h-6 text-blue-500 animate-spin" />;
    }
  };

  const handleConfirmKeywords = async () => {
    // TODO: 调用API确认关键词
    console.log('确认关键词:', confirmedKeywords);
    setShowKeywordConfirm(false);
  };

  const handleExport = (dataType: ExportDataType, format: ExportFormat) => {
    const filename = generateExportFilename(dataType, format);
    onExport({
      task_id: taskId,
      format,
      data_type: dataType,
      filename
    });
  };

  const renderProgressSteps = () => {
    const steps = [
      { key: 'keyword_processing', label: '关键词处理', progress: 20 },
      { key: 'link_discovering', label: '链接发现', progress: 40 },
      { key: 'comment_collecting', label: '评论采集', progress: 70 },
      { key: 'ai_analyzing', label: 'AI分析', progress: 90 },
      { key: 'completed', label: '完成', progress: 100 }
    ];

    const currentStepIndex = steps.findIndex(step => step.key === taskData.progress.status);
    
    return (
      <div className="space-y-4">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex || taskData.progress.status === 'completed';
          const isCurrent = index === currentStepIndex;
          const isUpcoming = index > currentStepIndex;

          return (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center
                ${isCompleted 
                  ? 'bg-green-100 text-green-600' 
                  : isCurrent 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'bg-gray-100 text-gray-400'
                }
              `}>
                {isCompleted ? (
                  <CheckCircleIcon className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>
              <div className="ml-4 flex-1">
                <p className={`text-sm font-medium ${
                  isCompleted ? 'text-green-600' : isCurrent ? 'text-blue-600' : 'text-gray-500'
                }`}>
                  {step.label}
                </p>
                {isCurrent && (
                  <div className="mt-1">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${taskData.progress.progress}%` }}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {taskData.progress.current_step}
                    </p>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderKeywordConfirmation = () => {
    if (!showKeywordConfirm || !taskData.processedKeywords) return null;

    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium text-blue-900 mb-4">
          请确认分析关键词
        </h3>
        
        {taskData.processedKeywords.clustered ? (
          <div className="space-y-4">
            <p className="text-sm text-blue-700">
              系统已将关键词聚类为以下主题，请选择要分析的关键词：
            </p>
            {taskData.processedKeywords.clustered.map((cluster, index) => (
              <div key={index} className="bg-white rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">{cluster.theme}</h4>
                <div className="space-y-2">
                  {cluster.keywords.map((keyword, keywordIndex) => (
                    <label key={keywordIndex} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={confirmedKeywords.includes(keyword)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setConfirmedKeywords([...confirmedKeywords, keyword]);
                          } else {
                            setConfirmedKeywords(confirmedKeywords.filter(k => k !== keyword));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{keyword}</span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : taskData.processedKeywords.expanded ? (
          <div>
            <p className="text-sm text-blue-700 mb-3">
              系统已扩展关键词，请确认要分析的关键词：
            </p>
            <div className="grid grid-cols-2 gap-2">
              {taskData.processedKeywords.expanded.map((keyword, index) => (
                <label key={index} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={confirmedKeywords.includes(keyword)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setConfirmedKeywords([...confirmedKeywords, keyword]);
                      } else {
                        setConfirmedKeywords(confirmedKeywords.filter(k => k !== keyword));
                      }
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{keyword}</span>
                </label>
              ))}
            </div>
          </div>
        ) : null}

        <div className="mt-4 flex justify-end space-x-3">
          <button
            onClick={() => setShowKeywordConfirm(false)}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleConfirmKeywords}
            disabled={confirmedKeywords.length === 0}
            className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
          >
            确认并继续（{confirmedKeywords.length}个关键词）
          </button>
        </div>
      </div>
    );
  };

  const renderTaskDetails = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 基本信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">任务信息</h4>
          <dl className="space-y-2">
            <div className="flex justify-between">
              <dt className="text-sm text-gray-500">任务ID</dt>
              <dd className="text-sm text-gray-900 font-mono">{taskId.slice(0, 8)}...</dd>
            </div>
            <div className="flex justify-between">
              <dt className="text-sm text-gray-500">扫描模式</dt>
              <dd className="text-sm text-gray-900">{formatScanMode(taskData.scan_mode)}</dd>
            </div>
            <div className="flex justify-between">
              <dt className="text-sm text-gray-500">创建时间</dt>
              <dd className="text-sm text-gray-900">
                {new Date(taskData.created_at).toLocaleString()}
              </dd>
            </div>
            <div className="flex justify-between">
              <dt className="text-sm text-gray-500">更新时间</dt>
              <dd className="text-sm text-gray-900">
                {new Date(taskData.updated_at).toLocaleString()}
              </dd>
            </div>
          </dl>
        </div>

        {/* 进度详情 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">进度详情</h4>
          {taskData.progress.details && (
            <dl className="space-y-2">
              {Object.entries(taskData.progress.details).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <dt className="text-sm text-gray-500">{key}</dt>
                  <dd className="text-sm text-gray-900">{value}</dd>
                </div>
              ))}
            </dl>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      {/* 任务状态头部 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          {getStatusIcon(taskData.progress.status)}
          <div className="ml-3">
            <h2 className="text-lg font-medium text-gray-900">
              {formatTaskStatus(taskData.progress.status)}
            </h2>
            <p className="text-sm text-gray-500">
              {taskData.progress.current_step}
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          {taskData.progress.status === 'completed' && (
            <>
              <button
                onClick={() => handleExport('report', 'markdown')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
                导出报告
              </button>
              <button
                onClick={() => handleExport('analysis', 'csv')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
                导出数据
              </button>
            </>
          )}
        </div>
      </div>

      {/* 关键词确认 */}
      {renderKeywordConfirmation()}

      {/* 进度条 */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>总体进度</span>
          <span>{taskData.progress.progress}%</span>
        </div>
        <div className="bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              taskData.progress.status === 'failed' ? 'bg-red-500' : 'bg-blue-600'
            }`}
            style={{ width: `${taskData.progress.progress}%` }}
          />
        </div>
      </div>

      {/* 步骤进度 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">执行步骤</h3>
        {renderProgressSteps()}
      </div>

      {/* 任务详情 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">任务详情</h3>
        {renderTaskDetails()}
      </div>

      {/* 错误信息 */}
      {taskData.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                任务执行失败
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {taskData.error}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskMonitor;
