/**
 * 洞察报告容器组件
 * 展示AI分析结果和市场机会洞察
 */

import React, { useState } from 'react';
import { 
  ChartBarIcon,
  LightBulbIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  StarIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';
import InsightCard from './InsightCard';

interface ReportContainerProps {
  analysis: AnalysisResult;
  taskData: EcommerceTaskData;
}

const ReportContainer: React.FC<ReportContainerProps> = ({
  analysis,
  taskData
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'opportunities' | 'details'>('overview');

  const renderOverview = () => {
    return (
      <div className="space-y-6">
        {/* 分析摘要 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <ChartBarIcon className="w-6 h-6 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">分析摘要</h3>
          </div>
          <p className="text-gray-700 leading-relaxed">
            {analysis.summary}
          </p>
        </div>

        {/* 关键指标 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">分析产品数</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {analysis.map_results?.length || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <LightBulbIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">发现机会</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {analysis.opportunities?.length || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUpIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">高优先级机会</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {analysis.opportunities?.filter(op => op.priority === 'high').length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 任务信息 */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">任务信息</h3>
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">扫描模式</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {taskData.scan_mode === 'deep' ? '深度扫描' : '快速扫描'}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">分析关键词</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {taskData.processed_keywords?.final?.length || 0} 个
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">分析时间</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(analysis.analyzed_at).toLocaleString()}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">任务耗时</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {taskData.completed_at && taskData.created_at ? 
                  Math.round((new Date(taskData.completed_at).getTime() - new Date(taskData.created_at).getTime()) / 1000 / 60) + ' 分钟'
                  : '计算中...'
                }
              </dd>
            </div>
          </dl>
        </div>
      </div>
    );
  };

  const renderOpportunities = () => {
    if (!analysis.opportunities || analysis.opportunities.length === 0) {
      return (
        <div className="text-center py-12">
          <LightBulbIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无市场机会</h3>
          <p className="mt-1 text-sm text-gray-500">
            当前分析未发现明显的市场机会点
          </p>
        </div>
      );
    }

    // 按优先级排序
    const sortedOpportunities = [...analysis.opportunities].sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return (priorityOrder[b.priority || 'medium'] || 2) - (priorityOrder[a.priority || 'medium'] || 2);
    });

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            市场机会洞察 ({analysis.opportunities.length})
          </h3>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <StarIcon className="w-4 h-4 text-yellow-500" />
            <span>按优先级排序</span>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {sortedOpportunities.map((opportunity, index) => (
            <InsightCard
              key={index}
              opportunity={opportunity}
              index={index}
            />
          ))}
        </div>
      </div>
    );
  };

  const renderDetails = () => {
    return (
      <div className="space-y-6">
        {/* 关键词分析 */}
        {taskData.processed_keywords && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">关键词分析</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">原始关键词</h4>
                <div className="flex flex-wrap gap-2">
                  {taskData.processed_keywords.original.map((keyword, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>

              {taskData.processed_keywords.final && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">最终分析关键词</h4>
                  <div className="flex flex-wrap gap-2">
                    {taskData.processed_keywords.final.map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {taskData.processed_keywords.clustered && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">关键词聚类</h4>
                  <div className="space-y-3">
                    {taskData.processed_keywords.clustered.map((cluster, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <h5 className="text-sm font-medium text-gray-900 mb-2">{cluster.theme}</h5>
                        <div className="flex flex-wrap gap-1">
                          {cluster.keywords.map((keyword, keywordIndex) => (
                            <span
                              key={keywordIndex}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-white text-gray-700 border"
                            >
                              {keyword}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 数据采集统计 */}
        {taskData.link_discovery && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">数据采集统计</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">链接发现</h4>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">淘宝链接</dt>
                    <dd className="text-sm text-gray-900">{taskData.link_discovery.taobao.length}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">小红书链接</dt>
                    <dd className="text-sm text-gray-900">{taskData.link_discovery.xiaohongshu.length}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">总链接数</dt>
                    <dd className="text-sm text-gray-900">{taskData.link_discovery.total}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">去重数量</dt>
                    <dd className="text-sm text-gray-900">{taskData.link_discovery.duplicates_removed}</dd>
                  </div>
                </dl>
              </div>

              {taskData.comment_collection && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">评论采集</h4>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">总评论数</dt>
                      <dd className="text-sm text-gray-900">{taskData.comment_collection.total_comments}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">成功链接</dt>
                      <dd className="text-sm text-gray-900">{taskData.comment_collection.successful_links}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">失败链接</dt>
                      <dd className="text-sm text-gray-900">{taskData.comment_collection.failed_links}</dd>
                    </div>
                  </dl>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Map阶段分析结果 */}
        {analysis.map_results && analysis.map_results.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              产品分析详情 ({analysis.map_results.length})
            </h3>
            
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {analysis.map_results.map((result, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">
                      产品 {index + 1}
                    </h4>
                    <span className={`
                      inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      ${result.platform === 'taobao' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'}
                    `}>
                      {result.platform === 'taobao' ? '淘宝' : '小红书'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <h5 className="font-medium text-green-700 mb-1">优点</h5>
                      <ul className="text-gray-600 space-y-1">
                        {result.advantages.map((advantage, i) => (
                          <li key={i}>• {advantage}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="font-medium text-red-700 mb-1">缺点</h5>
                      <ul className="text-gray-600 space-y-1">
                        {result.disadvantages.map((disadvantage, i) => (
                          <li key={i}>• {disadvantage}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="font-medium text-blue-700 mb-1">机会点</h5>
                      <ul className="text-gray-600 space-y-1">
                        {result.opportunities.map((opportunity, i) => (
                          <li key={i}>• {opportunity}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6">
      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'overview', label: '概览', icon: ChartBarIcon },
            { key: 'opportunities', label: '市场机会', icon: LightBulbIcon },
            { key: 'details', label: '详细数据', icon: DocumentTextIcon }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`
                group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                ${activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              <tab.icon className="w-5 h-5 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'opportunities' && renderOpportunities()}
        {activeTab === 'details' && renderDetails()}
      </div>
    </div>
  );
};

export default ReportContainer;
