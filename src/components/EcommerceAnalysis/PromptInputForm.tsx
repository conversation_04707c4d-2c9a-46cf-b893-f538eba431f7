/**
 * 关键词输入表单组件
 * 支持手动输入和文件上传两种方式
 */

import React, { useState, useRef } from 'react';
import { 
  DocumentTextIcon, 
  CloudArrowUpIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { parseKeywords, validateKeywordInput, formatScanMode } from '../../api/ecommerce';

interface PromptInputFormProps {
  onSubmit: (data: StartEcommerceTaskRequest) => void;
  loginStatus: LoginStatusResult | null;
  isLoading?: boolean;
}

const PromptInputForm: React.FC<PromptInputFormProps> = ({
  onSubmit,
  loginStatus,
  isLoading = false
}) => {
  const [scanMode, setScanMode] = useState<ScanMode>('quick');
  const [inputMode, setInputMode] = useState<'manual' | 'file'>('manual');
  const [keywordContent, setKeywordContent] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [excludeWords, setExcludeWords] = useState<string[]>([]);
  const [excludeInput, setExcludeInput] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const [previewKeywords, setPreviewKeywords] = useState<string[]>([]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理关键词内容变化
  const handleKeywordContentChange = (content: string) => {
    setKeywordContent(content);
    if (content.trim()) {
      const keywords = parseKeywords(content);
      setPreviewKeywords(keywords);
    } else {
      setPreviewKeywords([]);
    }
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // 读取文件内容
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        handleKeywordContentChange(content);
      };
      reader.readAsText(file);
    }
  };

  // 添加排除词
  const handleAddExcludeWord = () => {
    if (excludeInput.trim() && !excludeWords.includes(excludeInput.trim())) {
      setExcludeWords([...excludeWords, excludeInput.trim()]);
      setExcludeInput('');
    }
  };

  // 移除排除词
  const handleRemoveExcludeWord = (word: string) => {
    setExcludeWords(excludeWords.filter(w => w !== word));
  };

  // 表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const keywordInput: KeywordInput = {
      mode: inputMode,
      content: keywordContent,
      file: selectedFile,
      excludeWords: excludeWords.length > 0 ? excludeWords : undefined
    };

    // 验证输入
    const validation = validateKeywordInput(keywordInput);
    if (!validation.valid) {
      setErrors(validation.errors);
      return;
    }

    // 检查登录状态
    if (!loginStatus?.taobao.isLoggedIn && !loginStatus?.xiaohongshu.isLoggedIn) {
      setErrors(['请先登录淘宝或小红书账号']);
      return;
    }

    setErrors([]);
    onSubmit({
      scan_mode: scanMode,
      keyword_input: keywordInput
    });
  };

  const isFormValid = () => {
    const keywordInput: KeywordInput = {
      mode: inputMode,
      content: keywordContent,
      file: selectedFile
    };
    return validateKeywordInput(keywordInput).valid;
  };

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 扫描模式选择 */}
        <div>
          <label className="text-base font-medium text-gray-900">扫描模式</label>
          <p className="text-sm leading-5 text-gray-500">选择分析的深度和范围</p>
          <fieldset className="mt-4">
            <div className="space-y-4">
              {[
                {
                  value: 'quick' as ScanMode,
                  title: '快速扫描',
                  description: '快速获取基础洞察，适合初步市场调研'
                },
                {
                  value: 'deep' as ScanMode,
                  title: '深度扫描',
                  description: '全面分析市场机会，包含关键词扩展和聚类'
                }
              ].map((option) => (
                <div key={option.value} className="flex items-center">
                  <input
                    id={option.value}
                    name="scan-mode"
                    type="radio"
                    checked={scanMode === option.value}
                    onChange={(e) => setScanMode(option.value)}
                    className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
                  />
                  <label htmlFor={option.value} className="ml-3">
                    <span className="block text-sm font-medium text-gray-700">
                      {option.title}
                    </span>
                    <span className="block text-sm text-gray-500">
                      {option.description}
                    </span>
                  </label>
                </div>
              ))}
            </div>
          </fieldset>
        </div>

        {/* 关键词输入方式 */}
        <div>
          <label className="text-base font-medium text-gray-900">关键词输入</label>
          <div className="mt-4 space-y-4">
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setInputMode('manual')}
                className={`
                  flex-1 flex items-center justify-center px-4 py-3 border rounded-md text-sm font-medium
                  ${inputMode === 'manual'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  }
                `}
              >
                <DocumentTextIcon className="w-5 h-5 mr-2" />
                手动输入
              </button>
              <button
                type="button"
                onClick={() => setInputMode('file')}
                className={`
                  flex-1 flex items-center justify-center px-4 py-3 border rounded-md text-sm font-medium
                  ${inputMode === 'file'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  }
                `}
              >
                <CloudArrowUpIcon className="w-5 h-5 mr-2" />
                文件上传
              </button>
            </div>

            {/* 手动输入 */}
            {inputMode === 'manual' && (
              <div>
                <textarea
                  value={keywordContent}
                  onChange={(e) => handleKeywordContentChange(e.target.value)}
                  placeholder="请输入关键词，支持多种分隔符：逗号、顿号、空格、换行符等&#10;例如：手机壳、数据线、充电器"
                  rows={6}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
                <p className="mt-2 text-sm text-gray-500">
                  支持多种分隔符：中文逗号（、）、英文逗号（,）、空格、换行符、分号等
                </p>
              </div>
            )}

            {/* 文件上传 */}
            {inputMode === 'file' && (
              <div>
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md cursor-pointer hover:border-gray-400"
                >
                  <div className="space-y-1 text-center">
                    <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="flex text-sm text-gray-600">
                      <span className="relative font-medium text-blue-600 hover:text-blue-500">
                        点击上传文件
                      </span>
                      <p className="pl-1">或拖拽文件到此处</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      支持 TXT, CSV 文件，最大 10MB
                    </p>
                  </div>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".txt,.csv"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                {selectedFile && (
                  <p className="mt-2 text-sm text-gray-600">
                    已选择文件：{selectedFile.name}
                  </p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 关键词预览 */}
        {previewKeywords.length > 0 && (
          <div>
            <label className="text-base font-medium text-gray-900">
              关键词预览 ({previewKeywords.length}个)
            </label>
            <div className="mt-2 flex flex-wrap gap-2">
              {previewKeywords.slice(0, 20).map((keyword, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {keyword}
                </span>
              ))}
              {previewKeywords.length > 20 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  +{previewKeywords.length - 20}个
                </span>
              )}
            </div>
          </div>
        )}

        {/* 排除词设置 */}
        <div>
          <label className="text-base font-medium text-gray-900">排除词（可选）</label>
          <p className="text-sm text-gray-500">设置不希望包含的关键词</p>
          <div className="mt-2 flex space-x-2">
            <input
              type="text"
              value={excludeInput}
              onChange={(e) => setExcludeInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddExcludeWord())}
              placeholder="输入排除词"
              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <button
              type="button"
              onClick={handleAddExcludeWord}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              添加
            </button>
          </div>
          {excludeWords.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-2">
              {excludeWords.map((word, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                >
                  {word}
                  <button
                    type="button"
                    onClick={() => handleRemoveExcludeWord(word)}
                    className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-red-200"
                  >
                    <XCircleIcon className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* 错误提示 */}
        {errors.length > 0 && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  请修正以下问题：
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <ul className="list-disc pl-5 space-y-1">
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 登录状态提示 */}
        {loginStatus && (
          <div className="rounded-md bg-blue-50 p-4">
            <div className="flex">
              <InformationCircleIcon className="h-5 w-5 text-blue-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  平台登录状态
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      {loginStatus.taobao.isLoggedIn ? (
                        <CheckCircleIcon className="w-4 h-4 text-green-500 mr-1" />
                      ) : (
                        <XCircleIcon className="w-4 h-4 text-red-500 mr-1" />
                      )}
                      淘宝：{loginStatus.taobao.isLoggedIn ? '已登录' : '未登录'}
                    </span>
                    <span className="flex items-center">
                      {loginStatus.xiaohongshu.isLoggedIn ? (
                        <CheckCircleIcon className="w-4 h-4 text-green-500 mr-1" />
                      ) : (
                        <XCircleIcon className="w-4 h-4 text-red-500 mr-1" />
                      )}
                      小红书：{loginStatus.xiaohongshu.isLoggedIn ? '已登录' : '未登录'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 提交按钮 */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!isFormValid() || isLoading}
            className={`
              px-6 py-3 border border-transparent text-base font-medium rounded-md text-white
              ${isFormValid() && !isLoading
                ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                : 'bg-gray-400 cursor-not-allowed'
              }
            `}
          >
            {isLoading ? '启动中...' : '开始分析'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PromptInputForm;
