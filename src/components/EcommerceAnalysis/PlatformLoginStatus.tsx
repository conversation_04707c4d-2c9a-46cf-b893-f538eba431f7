/**
 * 平台登录状态组件
 * 显示淘宝和小红书的登录状态，并提供登录入口
 */

import React from 'react';
import { 
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';

interface PlatformLoginStatusProps {
  loginStatus: LoginStatusResult;
  onLoginClick: (platform: 'taobao' | 'xiaohongshu') => void;
}

const PlatformLoginStatus: React.FC<PlatformLoginStatusProps> = ({
  loginStatus,
  onLoginClick
}) => {
  const getPlatformIcon = (platform: 'taobao' | 'xiaohongshu') => {
    if (platform === 'taobao') {
      return (
        <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
          <span className="text-orange-600 font-bold text-sm">淘</span>
        </div>
      );
    } else {
      return (
        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
          <span className="text-red-600 font-bold text-sm">小</span>
        </div>
      );
    }
  };

  const getStatusIcon = (status: PlatformLoginStatus) => {
    if (status.isLoggedIn && status.sessionValid) {
      return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
    } else if (status.isLoggedIn && !status.sessionValid) {
      return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
    } else {
      return <XCircleIcon className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = (status: PlatformLoginStatus) => {
    if (status.isLoggedIn && status.sessionValid) {
      return '已登录';
    } else if (status.isLoggedIn && !status.sessionValid) {
      return '会话过期';
    } else {
      return '未登录';
    }
  };

  const getStatusColor = (status: PlatformLoginStatus) => {
    if (status.isLoggedIn && status.sessionValid) {
      return 'text-green-600';
    } else if (status.isLoggedIn && !status.sessionValid) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  };

  const needsLogin = !loginStatus.taobao.isLoggedIn && !loginStatus.xiaohongshu.isLoggedIn;
  const hasExpiredSession = 
    (loginStatus.taobao.isLoggedIn && !loginStatus.taobao.sessionValid) ||
    (loginStatus.xiaohongshu.isLoggedIn && !loginStatus.xiaohongshu.sessionValid);

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">平台登录状态</h3>
        <span className="text-xs text-gray-500">
          {new Date(loginStatus.lastChecked).toLocaleTimeString()}
        </span>
      </div>

      {/* 状态提示 */}
      {needsLogin && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <XCircleIcon className="w-4 h-4 text-red-500 mr-2" />
            <span className="text-sm text-red-700">
              请至少登录一个平台账号才能开始分析
            </span>
          </div>
        </div>
      )}

      {hasExpiredSession && (
        <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500 mr-2" />
            <span className="text-sm text-yellow-700">
              部分平台会话已过期，建议重新登录
            </span>
          </div>
        </div>
      )}

      {/* 平台状态列表 */}
      <div className="space-y-3">
        {/* 淘宝状态 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getPlatformIcon('taobao')}
            <div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">淘宝</span>
                {getStatusIcon(loginStatus.taobao)}
                <span className={`text-sm ${getStatusColor(loginStatus.taobao)}`}>
                  {getStatusText(loginStatus.taobao)}
                </span>
              </div>
              {loginStatus.taobao.lastLoginTime && (
                <p className="text-xs text-gray-500">
                  上次登录：{new Date(loginStatus.taobao.lastLoginTime).toLocaleString()}
                </p>
              )}
              {loginStatus.taobao.error && (
                <p className="text-xs text-red-500">
                  错误：{loginStatus.taobao.error}
                </p>
              )}
            </div>
          </div>
          
          {(!loginStatus.taobao.isLoggedIn || !loginStatus.taobao.sessionValid) && (
            <button
              onClick={() => onLoginClick('taobao')}
              className="inline-flex items-center px-3 py-1.5 border border-orange-300 text-sm font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-1" />
              登录
            </button>
          )}
        </div>

        {/* 小红书状态 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getPlatformIcon('xiaohongshu')}
            <div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">小红书</span>
                {getStatusIcon(loginStatus.xiaohongshu)}
                <span className={`text-sm ${getStatusColor(loginStatus.xiaohongshu)}`}>
                  {getStatusText(loginStatus.xiaohongshu)}
                </span>
              </div>
              {loginStatus.xiaohongshu.lastLoginTime && (
                <p className="text-xs text-gray-500">
                  上次登录：{new Date(loginStatus.xiaohongshu.lastLoginTime).toLocaleString()}
                </p>
              )}
              {loginStatus.xiaohongshu.error && (
                <p className="text-xs text-red-500">
                  错误：{loginStatus.xiaohongshu.error}
                </p>
              )}
            </div>
          </div>
          
          {(!loginStatus.xiaohongshu.isLoggedIn || !loginStatus.xiaohongshu.sessionValid) && (
            <button
              onClick={() => onLoginClick('xiaohongshu')}
              className="inline-flex items-center px-3 py-1.5 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-1" />
              登录
            </button>
          )}
        </div>
      </div>

      {/* 底部说明 */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          💡 提示：登录后可以获取更多商品信息和用户评论，提高分析质量
        </p>
      </div>
    </div>
  );
};

export default PlatformLoginStatus;
