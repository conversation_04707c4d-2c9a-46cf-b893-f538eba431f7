/**
 * 洞察卡片组件
 * 展示单个市场机会洞察
 */

import React, { useState } from 'react';
import { 
  LightBulbIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  StarIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface InsightCardProps {
  opportunity: MarketOpportunity;
  index: number;
}

const InsightCard: React.FC<InsightCardProps> = ({
  opportunity,
  index
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityLabel = (priority?: string) => {
    switch (priority) {
      case 'high':
        return '高优先级';
      case 'medium':
        return '中优先级';
      case 'low':
        return '低优先级';
      default:
        return '待评估';
    }
  };

  const getPriorityIcon = (priority?: string) => {
    const starCount = priority === 'high' ? 3 : priority === 'medium' ? 2 : 1;
    return Array.from({ length: starCount }, (_, i) => (
      <StarIcon key={i} className="w-4 h-4 fill-current" />
    ));
  };

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const handleCopyOpportunity = () => {
    const text = `
市场机会：${opportunity.title}

描述：
${opportunity.description}

支撑证据：
${opportunity.evidence.map((evidence, i) => `${i + 1}. ${evidence}`).join('\n')}

优先级：${getPriorityLabel(opportunity.priority)}
    `.trim();
    
    handleCopyToClipboard(text);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
      {/* 卡片头部 */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            {/* 序号图标 */}
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-blue-600">{index + 1}</span>
            </div>
            
            {/* 标题和优先级 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="text-lg font-medium text-gray-900 truncate">
                  {opportunity.title}
                </h3>
                
                {/* 优先级标签 */}
                <span className={`
                  inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border
                  ${getPriorityColor(opportunity.priority)}
                `}>
                  <span className="flex items-center space-x-1 mr-1">
                    {getPriorityIcon(opportunity.priority)}
                  </span>
                  {getPriorityLabel(opportunity.priority)}
                </span>
              </div>
              
              {/* 描述 */}
              <p className="text-gray-600 leading-relaxed">
                {opportunity.description}
              </p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2 ml-4">
            <button
              onClick={handleCopyOpportunity}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
              title="复制机会点"
            >
              <ClipboardDocumentIcon className="w-5 h-5" />
            </button>
            
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
              title={isExpanded ? "收起证据" : "展开证据"}
            >
              {isExpanded ? (
                <ChevronUpIcon className="w-5 h-5" />
              ) : (
                <ChevronDownIcon className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 支撑证据（可展开） */}
      {isExpanded && (
        <div className="px-6 pb-6">
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center mb-3">
              <LightBulbIcon className="w-5 h-5 text-yellow-500 mr-2" />
              <h4 className="text-sm font-medium text-gray-900">
                支撑证据 ({opportunity.evidence.length})
              </h4>
            </div>
            
            <div className="space-y-3">
              {opportunity.evidence.map((evidence, evidenceIndex) => (
                <div
                  key={evidenceIndex}
                  className="bg-gray-50 rounded-lg p-3 border-l-4 border-blue-200"
                >
                  <div className="flex items-start">
                    <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                      {evidenceIndex + 1}
                    </span>
                    <div className="flex-1">
                      <p className="text-sm text-gray-700 leading-relaxed">
                        "{evidence}"
                      </p>
                      <button
                        onClick={() => handleCopyToClipboard(evidence)}
                        className="mt-2 text-xs text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        复制这条证据
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 证据统计 */}
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-blue-700 font-medium">
                  证据强度评估
                </span>
                <div className="flex items-center space-x-1">
                  {Array.from({ length: 5 }, (_, i) => (
                    <div
                      key={i}
                      className={`w-2 h-2 rounded-full ${
                        i < Math.min(opportunity.evidence.length, 5)
                          ? 'bg-blue-500'
                          : 'bg-gray-300'
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-blue-600 text-xs">
                    {Math.min(opportunity.evidence.length, 5)}/5
                  </span>
                </div>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                基于 {opportunity.evidence.length} 条用户反馈
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 卡片底部操作栏 */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center">
              <LightBulbIcon className="w-4 h-4 mr-1" />
              {opportunity.evidence.length} 条证据
            </span>
            <span className="flex items-center">
              {getPriorityIcon(opportunity.priority)}
              <span className="ml-1">{getPriorityLabel(opportunity.priority)}</span>
            </span>
          </div>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {isExpanded ? '收起详情' : '查看详情'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default InsightCard;
