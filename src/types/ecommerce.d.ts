// 电商需求洞察应用 - 核心数据模型定义

declare global {
  // ==================== 扫描模式定义 ====================
  type ScanMode = 'quick' | 'deep';

  // ==================== 关键词处理相关 ====================
  interface KeywordInput {
    mode: 'manual' | 'file';
    content: string; // 手动输入的关键词字符串
    file?: File; // 上传的文件
    excludeWords?: string[]; // 排除词
  }

  interface ProcessedKeywords {
    original: string[]; // 原始关键词
    expanded?: string[]; // 扩展后的关键词（少于5个时）
    clustered?: KeywordCluster[]; // 聚类结果（5个以上时）
    final: string[]; // 最终确认的关键词
  }

  interface KeywordCluster {
    theme: string; // 主题名称
    keywords: string[]; // 该主题下的关键词
  }

  // ==================== 链接发现相关 ====================
  interface DiscoveredLink {
    url: string;
    platform: 'taobao' | 'xiaohongshu';
    keyword: string; // 对应的搜索关键词
    sales?: number; // 淘宝销量
    likes?: number; // 小红书点赞数
    title?: string; // 商品/帖子标题
    discoveredAt: string; // 发现时间
  }

  interface LinkDiscoveryResult {
    taobao: DiscoveredLink[];
    xiaohongshu: DiscoveredLink[];
    total: number;
    duplicatesRemoved: number;
  }

  // ==================== 评论采集相关 ====================
  interface CollectedComment {
    url: string;
    platform: 'taobao' | 'xiaohongshu';
    comments: string[]; // 评论文本数组
    collectedAt: string; // 采集时间
    commentCount: number; // 采集到的评论数量
  }

  interface CommentCollectionResult {
    links: CollectedComment[];
    totalComments: number;
    successfulLinks: number;
    failedLinks: number;
  }

  // ==================== AI分析相关 ====================
  interface ProductAnalysis {
    url: string;
    platform: 'taobao' | 'xiaohongshu';
    advantages: string[]; // 产品优点（最多3个）
    disadvantages: string[]; // 产品缺点（最多3个）
    opportunities: string[]; // 潜在机会点（最多2个）
  }

  interface MarketOpportunity {
    title: string; // 机会点标题
    description: string; // 详细描述
    evidence: string[]; // 支撑证据（原始评论）
    priority?: 'high' | 'medium' | 'low'; // 优先级
  }

  interface AnalysisResult {
    mapResults: ProductAnalysis[]; // Map阶段结果
    opportunities: MarketOpportunity[]; // Reduce阶段结果
    summary: string; // 分析总结
    analyzedAt: string; // 分析时间
  }

  // ==================== 电商分析任务状态 ====================
  type EcommerceTaskStatus = 
    | 'pending'           // 等待开始
    | 'keyword_processing' // 关键词处理中
    | 'user_confirming'   // 等待用户确认关键词
    | 'link_discovering'  // 链接发现中
    | 'link_deduplicating' // 链接去重中
    | 'comment_collecting' // 评论采集中
    | 'ai_analyzing'      // AI分析中
    | 'completed'         // 已完成
    | 'failed'           // 失败
    | 'paused';          // 暂停

  interface EcommerceTaskProgress {
    status: EcommerceTaskStatus;
    currentStep: string; // 当前步骤描述
    progress: number; // 进度百分比 0-100
    details?: {
      keywordsProcessed?: number;
      linksDiscovered?: number;
      linksDeduped?: number;
      commentsCollected?: number;
      productsAnalyzed?: number;
    };
  }

  // ==================== 电商分析任务数据 ====================
  interface EcommerceTaskData {
    taskId: string;
    scanMode: ScanMode;
    keywordInput: KeywordInput;
    processedKeywords?: ProcessedKeywords;
    linkDiscovery?: LinkDiscoveryResult;
    commentCollection?: CommentCollectionResult;
    analysis?: AnalysisResult;
    progress: EcommerceTaskProgress;
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
    error?: string;
  }

  // ==================== 数据导出相关 ====================
  type ExportFormat = 'markdown' | 'csv' | 'excel';
  type ExportDataType = 'links' | 'comments' | 'analysis' | 'report';

  interface ExportRequest {
    taskId: string;
    format: ExportFormat;
    dataType: ExportDataType;
    filename?: string;
  }

  interface ExportResult {
    success: boolean;
    filename: string;
    filePath: string;
    size: number;
    error?: string;
  }

  // ==================== 平台登录状态 ====================
  interface PlatformLoginStatus {
    platform: 'taobao' | 'xiaohongshu';
    isLoggedIn: boolean;
    sessionValid: boolean;
    fileExists: boolean;
    lastLoginTime?: string;
    error?: string;
  }

  interface LoginStatusResult {
    taobao: PlatformLoginStatus;
    xiaohongshu: PlatformLoginStatus;
    lastChecked: string;
  }

  // ==================== API请求/响应类型 ====================
  interface StartEcommerceTaskRequest {
    scanMode: ScanMode;
    keywordInput: KeywordInput;
  }

  interface StartEcommerceTaskResponse {
    taskId: string;
    status: EcommerceTaskStatus;
  }

  interface TaskStatusResponse {
    taskId: string;
    status: EcommerceTaskStatus;
    progress: EcommerceTaskProgress;
    data?: Partial<EcommerceTaskData>;
  }

  interface ConfirmKeywordsRequest {
    taskId: string;
    confirmedKeywords: string[];
  }

  // ==================== 扩展现有Task接口 ====================
  interface EcommerceTask extends Task {
    type: 'ecommerce_analysis';
    ecommerceData: EcommerceTaskData;
  }

  // ==================== 智能体相关类型 ====================
  type EcommerceAgentType = 
    | 'keyword_processor_agent'
    | 'ecommerce_crawler_agent' 
    | 'comment_analyzer_agent';

  interface EcommerceAgent extends Agent {
    type: EcommerceAgentType;
    ecommerceStatus?: {
      phase?: 'keyword_processing' | 'link_discovery' | 'deduplication' | 'comment_collection' | 'analysis';
      currentKeyword?: string;
      processedCount?: number;
      totalCount?: number;
    };
  }

  // ==================== 组件Props类型 ====================
  interface PromptInputFormProps {
    onSubmit: (data: StartEcommerceTaskRequest) => void;
    loginStatus: LoginStatusResult;
    isLoading?: boolean;
  }

  interface TaskMonitorProps {
    taskId: string;
    taskData: EcommerceTaskData;
    onExport: (request: ExportRequest) => void;
  }

  interface ReportContainerProps {
    analysis: AnalysisResult;
    taskData: EcommerceTaskData;
  }

  interface InsightCardProps {
    opportunity: MarketOpportunity;
    index: number;
  }

  interface PlatformLoginStatusProps {
    loginStatus: LoginStatusResult;
    onLoginClick: (platform: 'taobao' | 'xiaohongshu') => void;
  }
}

export {};
