/**
 * 登录状态管理Hook
 * 管理平台登录状态的检查和更新
 */

import { useState, useEffect, useCallback } from 'react';
import { ecommerceAPI } from '../api/ecommerce';

interface UseLoginStatusReturn {
  loginStatus: LoginStatusResult | null;
  isLoading: boolean;
  error: string | null;
  checkLoginStatus: () => Promise<void>;
  isAnyPlatformLoggedIn: boolean;
  needsLogin: boolean;
}

export const useLoginStatus = (autoCheck: boolean = true): UseLoginStatusReturn => {
  const [loginStatus, setLoginStatus] = useState<LoginStatusResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkLoginStatus = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const status = await ecommerceAPI.checkLoginStatus();
      setLoginStatus(status);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '检查登录状态失败';
      setError(errorMessage);
      console.error('检查登录状态失败:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 自动检查登录状态
  useEffect(() => {
    if (autoCheck) {
      checkLoginStatus();
    }
  }, [autoCheck, checkLoginStatus]);

  // 计算派生状态
  const isAnyPlatformLoggedIn = Boolean(
    loginStatus && (
      (loginStatus.taobao.is_logged_in && loginStatus.taobao.session_valid) ||
      (loginStatus.xiaohongshu.is_logged_in && loginStatus.xiaohongshu.session_valid)
    )
  );

  const needsLogin = Boolean(
    loginStatus && 
    !loginStatus.taobao.is_logged_in && 
    !loginStatus.xiaohongshu.is_logged_in
  );

  return {
    loginStatus,
    isLoading,
    error,
    checkLoginStatus,
    isAnyPlatformLoggedIn,
    needsLogin
  };
};
