/**
 * 电商需求洞察应用 - 前端API客户端
 * 处理与后端电商分析相关的API调用
 */

import { fetchPost, fetchGet, fetchPut, fetchDelete, getBaseURL } from './http';

// ==================== 类型定义 ====================
export interface StartEcommerceTaskRequest {
  scan_mode: ScanMode;
  keyword_input: KeywordInput;
}

export interface StartEcommerceTaskResponse {
  task_id: string;
  status: EcommerceTaskStatus;
}

export interface TaskStatusResponse {
  task_id: string;
  status: EcommerceTaskStatus;
  progress: EcommerceTaskProgress;
  data?: any;
}

export interface ConfirmKeywordsRequest {
  task_id: string;
  confirmed_keywords: string[];
}

export interface ExportRequest {
  task_id: string;
  format: ExportFormat;
  data_type: ExportDataType;
  filename?: string;
}

export interface ExportResponse {
  success: boolean;
  filename: string;
  file_path: string;
  size: number;
  error?: string;
}

export interface TaskListResponse {
  tasks: Array<{
    task_id: string;
    scan_mode: ScanMode;
    status: EcommerceTaskStatus;
    progress: number;
    created_at: string;
    updated_at: string;
  }>;
  total: number;
}

// ==================== API客户端类 ====================
export class EcommerceAPI {
  private baseURL: string = '';

  constructor() {
    this.initBaseURL();
  }

  private async initBaseURL() {
    this.baseURL = await getBaseURL();
  }

  /**
   * 启动电商分析任务
   */
  async startTask(request: StartEcommerceTaskRequest): Promise<StartEcommerceTaskResponse> {
    try {
      const response = await fetchPost('/ecommerce/task/start', request);
      return response;
    } catch (error) {
      console.error('启动电商分析任务失败:', error);
      throw error;
    }
  }

  /**
   * 查询任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskStatusResponse> {
    try {
      const response = await fetchGet(`/ecommerce/task/${taskId}/status`);
      return response;
    } catch (error) {
      console.error('查询任务状态失败:', error);
      throw error;
    }
  }

  /**
   * 确认关键词
   */
  async confirmKeywords(request: ConfirmKeywordsRequest): Promise<void> {
    try {
      await fetchPost(`/ecommerce/task/${request.task_id}/confirm-keywords`, request);
    } catch (error) {
      console.error('确认关键词失败:', error);
      throw error;
    }
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<void> {
    try {
      await fetchPost(`/ecommerce/task/${taskId}/pause`, {});
    } catch (error) {
      console.error('暂停任务失败:', error);
      throw error;
    }
  }

  /**
   * 恢复任务
   */
  async resumeTask(taskId: string): Promise<void> {
    try {
      await fetchPost(`/ecommerce/task/${taskId}/resume`, {});
    } catch (error) {
      console.error('恢复任务失败:', error);
      throw error;
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<void> {
    try {
      await fetchDelete(`/ecommerce/task/${taskId}`);
    } catch (error) {
      console.error('删除任务失败:', error);
      throw error;
    }
  }

  /**
   * 导出任务数据
   */
  async exportTaskData(request: ExportRequest): Promise<ExportResponse> {
    try {
      const response = await fetchGet(`/ecommerce/task/${request.task_id}/export`, request);
      return response;
    } catch (error) {
      console.error('导出任务数据失败:', error);
      throw error;
    }
  }

  /**
   * 检查平台登录状态
   */
  async checkLoginStatus(): Promise<LoginStatusResult> {
    try {
      const response = await fetchGet('/ecommerce/login/status');
      return response;
    } catch (error) {
      console.error('检查登录状态失败:', error);
      throw error;
    }
  }

  /**
   * 启动平台登录
   */
  async startPlatformLogin(platform: 'taobao' | 'xiaohongshu'): Promise<{ login_url: string; message: string }> {
    try {
      const response = await fetchPost(`/ecommerce/login/${platform}/start`, {});
      return response;
    } catch (error) {
      console.error(`启动${platform}登录失败:`, error);
      throw error;
    }
  }

  /**
   * 获取任务列表
   */
  async getTaskList(limit: number = 20, offset: number = 0): Promise<TaskListResponse> {
    try {
      const response = await fetchGet('/ecommerce/tasks', { limit, offset });
      return response;
    } catch (error) {
      console.error('获取任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建任务状态流（SSE）
   */
  createTaskStatusStream(taskId: string, onMessage: (data: any) => void, onError?: (error: Error) => void): EventSource {
    const eventSource = new EventSource(`${this.baseURL}/ecommerce/task/${taskId}/stream`);
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('解析SSE数据失败:', error);
        onError?.(error as Error);
      }
    };

    eventSource.onerror = (event) => {
      console.error('SSE连接错误:', event);
      onError?.(new Error('SSE连接错误'));
    };

    return eventSource;
  }
}

// ==================== 工具函数 ====================

/**
 * 解析关键词字符串
 */
export function parseKeywords(content: string): string[] {
  // 支持多种分隔符：中文逗号、英文逗号、顿号、空格、换行符、分号
  const keywords = content.split(/[、，,\s\n;；]+/)
    .map(kw => kw.trim())
    .filter(kw => kw.length > 0);
  
  return [...new Set(keywords)]; // 去重
}

/**
 * 验证关键词输入
 */
export function validateKeywordInput(input: KeywordInput): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (input.mode === 'manual') {
    if (!input.content.trim()) {
      errors.push('请输入关键词');
    } else {
      const keywords = parseKeywords(input.content);
      if (keywords.length === 0) {
        errors.push('未识别到有效关键词');
      } else if (keywords.length > 50) {
        errors.push('关键词数量不能超过50个');
      }
    }
  } else if (input.mode === 'file') {
    if (!input.file) {
      errors.push('请选择文件');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 格式化任务状态显示文本
 */
export function formatTaskStatus(status: EcommerceTaskStatus): string {
  const statusMap: Record<EcommerceTaskStatus, string> = {
    pending: '等待开始',
    keyword_processing: '关键词处理中',
    user_confirming: '等待用户确认',
    link_discovering: '链接发现中',
    link_deduplicating: '链接去重中',
    comment_collecting: '评论采集中',
    ai_analyzing: 'AI分析中',
    completed: '已完成',
    failed: '失败',
    paused: '已暂停'
  };

  return statusMap[status] || status;
}

/**
 * 格式化扫描模式显示文本
 */
export function formatScanMode(mode: ScanMode): string {
  return mode === 'deep' ? '深度扫描' : '快速扫描';
}

/**
 * 计算任务进度颜色
 */
export function getProgressColor(status: EcommerceTaskStatus): string {
  switch (status) {
    case 'completed':
      return 'text-green-600';
    case 'failed':
      return 'text-red-600';
    case 'paused':
      return 'text-yellow-600';
    case 'user_confirming':
      return 'text-blue-600';
    default:
      return 'text-blue-500';
  }
}

/**
 * 生成导出文件名
 */
export function generateExportFilename(dataType: ExportDataType, format: ExportFormat): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  const typeMap: Record<ExportDataType, string> = {
    links: '链接数据',
    comments: '评论数据',
    analysis: '分析数据',
    report: '洞察报告'
  };
  
  return `电商分析_${typeMap[dataType]}_${timestamp}.${format}`;
}

// 创建全局API实例
export const ecommerceAPI = new EcommerceAPI();
