/**
 * 电商需求洞察分析 - 主页面
 * 整合关键词输入、任务监控、洞察报告等功能
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  ShoppingBagIcon, 
  ChartBarIcon, 
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

import PromptInputForm from '../components/EcommerceAnalysis/PromptInputForm';
import TaskMonitor from '../components/EcommerceAnalysis/TaskMonitor';
import ReportContainer from '../components/EcommerceAnalysis/ReportContainer';
import PlatformLoginStatus from '../components/EcommerceAnalysis/PlatformLoginStatus';
import LoginGuide from '../components/LoginGuide';

import { ecommerceAPI } from '../api/ecommerce';
import { useChatStore } from '../store/chatStore';
import { useLoginStatus } from '../hooks/useLoginStatus';

const EcommerceAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const { currentTask } = useChatStore();

  // 登录状态管理
  const {
    loginStatus,
    isLoading: isCheckingLogin,
    checkLoginStatus,
    needsLogin
  } = useLoginStatus();

  // 页面状态
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [taskData, setTaskData] = useState<EcommerceTaskData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'input' | 'monitor' | 'report'>('input');
  const [showLoginGuide, setShowLoginGuide] = useState(false);

  // 检查是否需要显示登录引导
  useEffect(() => {
    if (loginStatus && needsLogin) {
      setShowLoginGuide(true);
    }
  }, [loginStatus, needsLogin]);

  // 监控任务状态
  useEffect(() => {
    if (currentTaskId) {
      const interval = setInterval(() => {
        fetchTaskStatus();
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [currentTaskId]);

  // 移除原来的checkLoginStatus函数，使用hook中的

  const fetchTaskStatus = async () => {
    if (!currentTaskId) return;

    try {
      const response = await ecommerceAPI.getTaskStatus(currentTaskId);
      
      // 更新任务数据
      if (response.data) {
        setTaskData({
          task_id: response.task_id,
          scan_mode: taskData?.scan_mode || 'quick',
          keyword_input: taskData?.keyword_input || { mode: 'manual', content: '' },
          progress: response.progress,
          created_at: taskData?.created_at || new Date().toISOString(),
          updated_at: new Date().toISOString(),
          ...response.data
        } as EcommerceTaskData);
      }

      // 根据状态切换标签页
      if (response.status === 'user_confirming' && activeTab === 'input') {
        setActiveTab('monitor');
      } else if (response.status === 'completed' && activeTab !== 'report') {
        setActiveTab('report');
        toast.success('分析完成！');
      } else if (response.status === 'failed') {
        toast.error('任务执行失败');
      }

    } catch (error) {
      console.error('获取任务状态失败:', error);
    }
  };

  const handleStartTask = async (request: StartEcommerceTaskRequest) => {
    setIsLoading(true);
    try {
      const response = await ecommerceAPI.startTask(request);
      setCurrentTaskId(response.task_id);
      
      // 创建初始任务数据
      setTaskData({
        task_id: response.task_id,
        scan_mode: request.scan_mode,
        keyword_input: request.keyword_input,
        progress: {
          status: response.status,
          current_step: '任务已创建',
          progress: 0,
          details: {}
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      setActiveTab('monitor');
      toast.success('任务已启动');
    } catch (error) {
      console.error('启动任务失败:', error);
      toast.error('启动任务失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (request: ExportRequest) => {
    try {
      const response = await ecommerceAPI.exportTaskData(request);
      if (response.success) {
        toast.success(`导出成功：${response.filename}`);
        // TODO: 触发文件下载
      } else {
        toast.error(`导出失败：${response.error}`);
      }
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败');
    }
  };

  const handleLoginClick = async (platform: 'taobao' | 'xiaohongshu') => {
    try {
      const response = await ecommerceAPI.startPlatformLogin(platform);

      // 打开登录窗口
      const loginWindow = window.open(response.login_url, '_blank', 'width=800,height=600');

      // 监听登录完成
      const checkLoginComplete = () => {
        if (loginWindow?.closed) {
          // 窗口关闭，重新检查登录状态
          setTimeout(() => {
            checkLoginStatus();
          }, 1000);
        } else {
          setTimeout(checkLoginComplete, 1000);
        }
      };

      setTimeout(checkLoginComplete, 2000);

      toast.success(response.message);
    } catch (error) {
      console.error(`启动${platform}登录失败:`, error);
      toast.error(`启动${platform}登录失败`);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'input':
        return (
          <PromptInputForm
            onSubmit={handleStartTask}
            loginStatus={loginStatus}
            isLoading={isLoading}
          />
        );
      case 'monitor':
        return taskData ? (
          <TaskMonitor
            taskId={currentTaskId!}
            taskData={taskData}
            onExport={handleExport}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            没有正在执行的任务
          </div>
        );
      case 'report':
        return taskData?.analysis ? (
          <ReportContainer
            analysis={taskData.analysis}
            taskData={taskData}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            暂无分析报告
          </div>
        );
      default:
        return null;
    }
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'input':
        return <ShoppingBagIcon className="w-5 h-5" />;
      case 'monitor':
        return <ClockIcon className="w-5 h-5" />;
      case 'report':
        return <ChartBarIcon className="w-5 h-5" />;
      default:
        return null;
    }
  };

  const getTabStatus = (tab: string) => {
    if (!taskData) return null;
    
    switch (tab) {
      case 'input':
        return currentTaskId ? 'completed' : 'current';
      case 'monitor':
        if (!currentTaskId) return 'disabled';
        return taskData.progress.status === 'completed' ? 'completed' : 'current';
      case 'report':
        return taskData.progress.status === 'completed' ? 'current' : 'disabled';
      default:
        return null;
    }
  };

  return (
    <>
      {/* 登录引导弹窗 */}
      {showLoginGuide && (
        <LoginGuide
          onComplete={() => setShowLoginGuide(false)}
          onSkip={() => setShowLoginGuide(false)}
        />
      )}

      <div className="min-h-screen bg-gray-50">
        {/* 页面头部 */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  电商需求洞察分析
                </h1>
                <p className="mt-2 text-gray-600">
                  通过AI驱动的智能分析，发现电商市场机会
                </p>
              </div>

              {/* 平台登录状态 */}
              {loginStatus && (
                <PlatformLoginStatus
                  loginStatus={loginStatus}
                  onLoginClick={handleLoginClick}
                />
              )}
            </div>
          </div>
        </div>

      {/* 标签页导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'input', label: '需求输入' },
              { key: 'monitor', label: '任务监控' },
              { key: 'report', label: '洞察报告' }
            ].map((tab) => {
              const status = getTabStatus(tab.key);
              const isActive = activeTab === tab.key;
              const isDisabled = status === 'disabled';
              
              return (
                <button
                  key={tab.key}
                  onClick={() => !isDisabled && setActiveTab(tab.key as any)}
                  disabled={isDisabled}
                  className={`
                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                    ${isActive
                      ? 'border-blue-500 text-blue-600'
                      : isDisabled
                      ? 'border-transparent text-gray-400 cursor-not-allowed'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <span className="mr-2">
                    {getTabIcon(tab.key)}
                  </span>
                  {tab.label}
                  
                  {/* 状态指示器 */}
                  {status === 'completed' && (
                    <CheckCircleIcon className="ml-2 w-4 h-4 text-green-500" />
                  )}
                  {status === 'current' && activeTab !== tab.key && (
                    <ExclamationTriangleIcon className="ml-2 w-4 h-4 text-yellow-500" />
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border">
          {renderTabContent()}
        </div>
      </div>
    </>
  );
};

export default EcommerceAnalysis;
